2025-06-04 09:36:10,183 - app - INFO - IATA XML Generator startup
2025-06-04 09:36:10,183 - app - INFO - Static folder path: /var/www/xmlmaker/frontend/static
2025-06-04 09:36:10,200 - app - INFO - IATA XML Generator startup
2025-06-04 09:36:10,200 - app - INFO - Static folder path: /var/www/xmlmaker/frontend/static
2025-06-04 09:36:10,238 - app - INFO - IATA XML Generator startup
2025-06-04 09:36:10,238 - app - INFO - Static folder path: /var/www/xmlmaker/frontend/static
2025-06-04 09:36:10,425 - app - INFO - IATA XML Generator startup
2025-06-04 09:36:10,425 - app - INFO - Static folder path: /var/www/xmlmaker/frontend/static
2025-06-04 09:37:41,346 - app - INFO - Serving index.html
2025-06-04 09:44:26,983 - app - INFO - Serving index.html
2025-06-04 09:49:16,773 - app - INFO - Serving index.html
2025-06-04 09:49:18,803 - app - INFO - Serving static file: favicon.ico
2025-06-04 10:52:30,232 - app - INFO - Received Flight Manifest XML generation request
2025-06-04 10:52:30,232 - app - INFO - Input text length: 90
2025-06-04 10:52:30,232 - app - INFO - Generating Flight Manifest XML...
2025-06-04 10:52:30,232 - app - INFO - Processing 5 lines of input
2025-06-04 10:52:30,232 - app - INFO - Processing line: FFM/8
2025-06-04 10:52:30,232 - app - INFO - Processing line: 1/KQ738/17MAY/NBO/5Y-KYT
2025-06-04 10:52:30,232 - app - INFO - Processing line: LLW
2025-06-04 10:52:30,232 - app - INFO - Processing line: 706-60498093BOMLLW/T10K196.0MC1.17/COURIER MATERIA
2025-06-04 10:52:30,232 - app - INFO - Found AWB line: 706-60498093BOMLLW/T10K196.0MC1.17/COURIER MATERIA
2025-06-04 10:52:30,233 - app - INFO - Adding AWB: {'prefix': '706', 'number': '60498093', 'origin': 'BOM', 'destination': 'LLW', 'split_code': 'T', 'pieces': '10', 'weight': '196.0', 'volume': '1.17', 'description': 'COURIER MATERIA', 'special_codes': [], 'uld': None}
2025-06-04 10:52:30,233 - app - INFO - Processing line: /COU
2025-06-04 10:52:30,233 - app - INFO - Found special code line: /COU
2025-06-04 10:52:30,233 - app - INFO - Found 1 AWBs in the input
2025-06-04 10:52:30,233 - app - INFO - Found flight line: 1/KQ738/17MAY/NBO/5Y-KYT
2025-06-04 10:52:30,233 - app - INFO - Found arrival airport: LLW
2025-06-04 10:52:30,233 - app - INFO - Flight details: KQ738, 17MAY, NBO, LLW, 5Y-KYT
2025-06-04 10:52:30,234 - app - INFO - Formatted departure date: 2025-05-17T00:00:00
2025-06-04 10:52:30,234 - app - INFO - Generating XML for ULD: PMC01921KQ
2025-06-04 10:52:30,234 - app - INFO - Adding AWB to ULD 0: 706-60498093
2025-06-04 10:52:30,234 - app - INFO - XML generated successfully, length: 3550
2025-06-04 10:53:08,198 - app - INFO - Serving static file: xfwb.html
2025-06-04 10:53:08,786 - app - INFO - Serving static file: css/styles.css
2025-06-04 10:53:08,794 - app - INFO - Serving static file: js/xfwb.js
2025-06-04 10:53:43,680 - app - INFO - Received XFWB XML generation request
2025-06-04 10:53:43,680 - app - INFO - Input text length: 50
2025-06-04 10:53:43,680 - app - INFO - Generating XFWB XML...
2025-06-04 10:53:43,680 - xfwb_generator - INFO - Parsing input text: 706-60498093BOMLLW/T10K196.0MC1.17/COURIER MATERIA
2025-06-04 10:53:43,681 - xfwb_generator - INFO - Found AWB: 706-60498093
2025-06-04 10:53:43,681 - xfwb_generator - INFO - Parsed 1 AWBs
2025-06-04 10:53:43,682 - app - INFO - XFWB XML generated successfully, length: 4589
2025-06-04 11:59:35,234 - app - INFO - Serving static file: xfwb.html
2025-06-04 17:15:26,435 - app - INFO - IATA XML Generator startup
2025-06-04 17:15:26,445 - app - INFO - Static folder path: /var/www/xmlmaker/frontend/static
2025-06-04 17:15:26,454 - app - INFO - IATA XML Generator startup
2025-06-04 17:15:26,454 - app - INFO - Static folder path: /var/www/xmlmaker/frontend/static
2025-06-04 17:15:26,438 - app - INFO - IATA XML Generator startup
2025-06-04 17:15:26,456 - app - INFO - Static folder path: /var/www/xmlmaker/frontend/static
2025-06-04 17:15:26,461 - app - INFO - IATA XML Generator startup
2025-06-04 17:15:26,462 - app - INFO - Static folder path: /var/www/xmlmaker/frontend/static
2025-06-05 13:47:24,541 - app - INFO - IATA XML Generator startup
2025-06-05 13:47:24,546 - app - INFO - Static folder path: /var/www/xmlmaker/frontend/static
2025-06-05 13:47:24,560 - app - INFO - IATA XML Generator startup
2025-06-05 13:47:24,560 - app - INFO - Static folder path: /var/www/xmlmaker/frontend/static
2025-06-05 13:47:24,565 - app - INFO - IATA XML Generator startup
2025-06-05 13:47:24,565 - app - INFO - Static folder path: /var/www/xmlmaker/frontend/static
2025-06-05 13:47:24,568 - app - INFO - IATA XML Generator startup
2025-06-05 13:47:24,569 - app - INFO - Static folder path: /var/www/xmlmaker/frontend/static
2025-06-06 08:58:58,033 - app - INFO - Serving index.html
2025-06-06 08:58:59,418 - app - INFO - Serving static file: favicon.ico
2025-06-06 08:59:20,015 - app - INFO - Received Flight Manifest XML generation request
2025-06-06 08:59:20,015 - app - INFO - Input text length: 148
2025-06-06 08:59:20,015 - app - INFO - Generating Flight Manifest XML...
2025-06-06 08:59:20,016 - app - INFO - Processing 8 lines of input
2025-06-06 08:59:20,016 - app - INFO - Processing line: FFM/8
2025-06-06 08:59:20,016 - app - INFO - Processing line: 1/KQ756/05JUN/NBO/5Y-FFH
2025-06-06 08:59:20,017 - app - INFO - Processing line: LLW
2025-06-06 08:59:20,017 - app - INFO - Processing line: 706-41045174NBOLLW/T1K40.0MC0.01/MOISTURE GAUGE
2025-06-06 08:59:20,017 - app - INFO - Found AWB line: 706-41045174NBOLLW/T1K40.0MC0.01/MOISTURE GAUGE
2025-06-06 08:59:20,018 - app - INFO - Adding AWB: {'prefix': '706', 'number': '41045174', 'origin': 'NBO', 'destination': 'LLW', 'split_code': 'T', 'pieces': '1', 'weight': '40.0', 'volume': '0.01', 'description': 'MOISTURE GAUGE', 'special_codes': [], 'uld': None}
2025-06-06 08:59:20,018 - app - INFO - Processing line: /COU/XPS
2025-06-06 08:59:20,018 - app - INFO - Found special code line: /COU/XPS
2025-06-06 08:59:20,018 - app - INFO - Processing line: 706-60612602NBOLLW/T1K2.0MC0.01/DHL EXPRESS
2025-06-06 08:59:20,018 - app - INFO - Found AWB line: 706-60612602NBOLLW/T1K2.0MC0.01/DHL EXPRESS
2025-06-06 08:59:20,018 - app - INFO - Adding AWB: {'prefix': '706', 'number': '60612602', 'origin': 'NBO', 'destination': 'LLW', 'split_code': 'T', 'pieces': '1', 'weight': '2.0', 'volume': '0.01', 'description': 'DHL EXPRESS', 'special_codes': [], 'uld': None}
2025-06-06 08:59:20,019 - app - INFO - Processing line: /COU
2025-06-06 08:59:20,019 - app - INFO - Found special code line: /COU
2025-06-06 08:59:20,019 - app - INFO - Processing line: APL/NIL
2025-06-06 08:59:20,019 - app - INFO - Found 2 AWBs in the input
2025-06-06 08:59:20,020 - app - INFO - Found flight line: 1/KQ756/05JUN/NBO/5Y-FFH
2025-06-06 08:59:20,020 - app - INFO - Found arrival airport: LLW
2025-06-06 08:59:20,020 - app - INFO - Flight details: KQ756, 05JUN, NBO, LLW, 5Y-FFH
2025-06-06 08:59:20,020 - app - INFO - Formatted departure date: 2025-06-05T00:00:00
2025-06-06 08:59:20,021 - app - INFO - Generating XML for ULD: PMC01921KQ
2025-06-06 08:59:20,021 - app - INFO - Adding AWB to ULD 0: 706-41045174
2025-06-06 08:59:20,021 - app - INFO - Adding AWB to ULD 0: 706-60612602
2025-06-06 08:59:20,021 - app - INFO - XML generated successfully, length: 4565
2025-06-06 08:59:23,397 - app - INFO - Serving static file: xfwb.html
2025-06-06 08:59:23,603 - app - INFO - Serving static file: css/styles.css
2025-06-06 08:59:23,612 - app - INFO - Serving static file: js/xfwb.js
2025-06-06 09:03:42,928 - app - INFO - Serving static file: xfwb.html
2025-06-06 09:03:49,472 - app - INFO - Serving index.html
2025-06-06 09:04:04,884 - app - INFO - Serving index.html
2025-06-06 09:04:06,365 - app - INFO - Serving index.html
2025-06-06 09:06:13,039 - app - INFO - Serving index.html
2025-06-06 09:15:38,436 - app - INFO - Serving index.html
2025-06-06 09:15:40,127 - app - INFO - Serving static file: xfwb.html
2025-06-06 09:15:43,840 - app - INFO - Serving static file: xfzb.html
2025-06-06 09:15:47,970 - app - INFO - Serving static file: xfwb.html
2025-06-06 09:15:50,178 - app - INFO - Serving static file: xfwb.html
2025-06-06 09:16:50,355 - app - INFO - Serving static file: xfzb.html
2025-06-06 09:24:33,788 - app - INFO - IATA XML Generator startup
2025-06-06 09:24:33,788 - app - INFO - Static folder path: /var/www/xmlmaker/frontend/static
2025-06-06 09:24:33,791 - app - INFO - Starting server on port 5002
2025-06-06 09:24:33,795 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-06-06 09:24:33,795 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-06 09:24:33,797 - werkzeug - INFO -  * Restarting with stat
2025-06-06 09:24:33,926 - app - INFO - IATA XML Generator startup
2025-06-06 09:24:33,926 - app - INFO - Static folder path: /var/www/xmlmaker/frontend/static
2025-06-06 09:24:33,930 - app - INFO - Starting server on port 5002
2025-06-06 09:24:33,932 - werkzeug - WARNING -  * Debugger is active!
2025-06-06 09:24:33,945 - werkzeug - INFO -  * Debugger PIN: 990-968-340
2025-06-06 09:25:06,671 - app - INFO - XFZB sample data requested
2025-06-06 09:25:06,671 - app - INFO - XFZB sample data sent
2025-06-06 09:25:06,677 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 09:25:06] "GET /api/sample-xfzb HTTP/1.1" 200 -
2025-06-06 09:25:17,925 - app - INFO - Received XFZB XML generation request
2025-06-06 09:25:17,926 - app - INFO - Input text length: 40
2025-06-06 09:25:17,926 - app - INFO - Generating XFZB XML...
2025-06-06 09:25:17,926 - xfzb_generator - INFO - Parsing input text: SHAS51282599/T1K4.0MC0.027/panel pc
/EAW
2025-06-06 09:25:17,926 - xfzb_generator - INFO - Found House AWB: SHAS51282599
2025-06-06 09:25:17,926 - xfzb_generator - INFO - Added Handling Instructions codes to House AWB SHAS51282599: ['EAW']
2025-06-06 09:25:17,926 - xfzb_generator - INFO - Parsed 1 House AWBs
2025-06-06 09:25:17,927 - app - INFO - XFZB XML generated successfully, length: 6168
2025-06-06 09:25:17,927 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 09:25:17] "POST /api/generate-xfzb HTTP/1.1" 200 -
2025-06-06 09:26:23,928 - app - INFO - Received XFZB XML generation request
2025-06-06 09:26:23,928 - app - INFO - Input text length: 35
2025-06-06 09:26:23,929 - app - INFO - Generating XFZB XML...
2025-06-06 09:26:23,929 - xfzb_generator - INFO - Parsing input text: SHAS51282599/T1K4.0MC0.027/panel pc
2025-06-06 09:26:23,929 - xfzb_generator - INFO - Found House AWB: SHAS51282599
2025-06-06 09:26:23,929 - xfzb_generator - INFO - Parsed 1 House AWBs
2025-06-06 09:26:23,929 - app - INFO - XFZB XML generated successfully, length: 6040
2025-06-06 09:26:23,931 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 09:26:23] "POST /api/generate-xfzb HTTP/1.1" 200 -
2025-06-06 09:30:52,238 - app - INFO - Serving index.html
2025-06-06 09:31:16,568 - app - INFO - Serving static file: xfwb.html
2025-06-06 09:31:19,673 - app - INFO - Serving static file: xfzb.html
2025-06-06 09:31:23,572 - app - INFO - Serving static file: xfzb.html
2025-06-06 09:31:23,575 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 09:31:23] "GET /xfzb.html HTTP/1.1" 200 -
2025-06-06 09:31:24,487 - app - INFO - Serving static file: favicon.ico
2025-06-06 09:31:24,487 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 09:31:24] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-06-06 09:37:14,250 - app - INFO - Serving static file: xfwb.html
2025-06-06 09:37:22,052 - app - INFO - Serving static file: xfzb.html
2025-06-06 09:38:34,715 - app - INFO - Serving index.html
2025-06-06 09:38:36,008 - app - INFO - Serving static file: xfwb.html
2025-06-06 09:38:39,708 - app - INFO - Serving static file: index.html
2025-06-06 09:38:40,859 - app - INFO - Serving static file: xfwb.html
2025-06-06 09:38:42,450 - app - INFO - Serving static file: index.html
2025-06-06 09:38:43,498 - app - INFO - Serving static file: xfwb.html
2025-06-06 09:38:46,745 - app - INFO - XFWB sample data requested
2025-06-06 09:38:46,745 - app - INFO - XFWB sample data sent
2025-06-06 09:38:48,909 - app - INFO - Received XFWB XML generation request
2025-06-06 09:38:48,909 - app - INFO - Input text length: 102
2025-06-06 09:38:48,909 - app - INFO - Generating XFWB XML...
2025-06-06 09:38:48,909 - xfwb_generator - INFO - Parsing input text: 706-51663054NBOLLW/T1K138.0MC1.01/HUMAN REMAINS
/HUM
706-60609942NBOLLW/T4K25.0MC0.01/DHL EXPRESS
/COU
2025-06-06 09:38:48,910 - xfwb_generator - INFO - Found AWB: 706-51663054
2025-06-06 09:38:48,910 - xfwb_generator - INFO - Added Handling Instructions codes to AWB 706-51663054: ['HUM']
2025-06-06 09:38:48,910 - xfwb_generator - INFO - Found AWB: 706-60609942
2025-06-06 09:38:48,910 - xfwb_generator - INFO - Added Handling Instructions codes to AWB 706-60609942: ['COU']
2025-06-06 09:38:48,910 - xfwb_generator - INFO - Parsed 2 AWBs
2025-06-06 09:38:48,910 - xfwb_generator - INFO - Generating consolidated XML for 2 AWBs
2025-06-06 09:38:48,910 - xfwb_generator - INFO - Processing AWB 1/2: 706-51663054
2025-06-06 09:38:48,910 - xfwb_generator - INFO - Processing AWB 2/2: 706-60609942
2025-06-06 09:38:48,911 - app - INFO - XFWB XML generated successfully, length: 9452
2025-06-06 09:38:48,911 - app - INFO - Multiple AWBs detected in the response
2025-06-06 09:38:48,911 - app - INFO - Found 2 AWBs in the response
2025-06-06 09:38:52,141 - app - INFO - Serving static file: xfzb.html
2025-06-06 09:38:53,757 - app - INFO - Serving static file: xfwb.html
2025-06-06 09:42:51,007 - app - INFO - Serving static file: xfwb.html
2025-06-06 09:42:51,007 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 09:42:51] "GET /xfwb.html HTTP/1.1" 200 -
2025-06-06 09:43:02,229 - app - INFO - Serving static file: xfzb.html
2025-06-06 09:43:02,230 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 09:43:02] "GET /xfzb.html HTTP/1.1" 200 -
2025-06-06 09:43:12,267 - app - INFO - Serving static file: index.html
2025-06-06 09:43:12,267 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 09:43:12] "GET /index.html HTTP/1.1" 200 -
2025-06-06 09:43:18,894 - app - INFO - Serving static file: xfwb.html
2025-06-06 09:43:18,894 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 09:43:18] "[36mGET /xfwb.html HTTP/1.1[0m" 304 -
2025-06-06 09:43:21,605 - app - INFO - Serving static file: xfwb.html
2025-06-06 09:43:23,207 - app - INFO - XFWB sample data requested
2025-06-06 09:43:23,208 - app - INFO - XFWB sample data sent
2025-06-06 09:43:23,208 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 09:43:23] "GET /api/sample-xfwb HTTP/1.1" 200 -
2025-06-06 09:43:23,588 - app - INFO - Serving static file: xfzb.html
2025-06-06 09:43:24,940 - app - INFO - Serving static file: xfwb.html
2025-06-06 09:43:26,091 - app - INFO - Serving static file: xfzb.html
2025-06-06 09:43:27,468 - app - INFO - Serving static file: xfwb.html
2025-06-06 09:43:28,777 - app - INFO - Serving static file: xfzb.html
2025-06-06 09:43:29,838 - app - INFO - Serving static file: xfwb.html
2025-06-06 09:43:30,995 - app - INFO - XFWB sample data requested
2025-06-06 09:43:30,995 - app - INFO - XFWB sample data sent
2025-06-06 09:43:34,744 - app - INFO - Received XFWB XML generation request
2025-06-06 09:43:34,744 - app - INFO - Input text length: 102
2025-06-06 09:43:34,744 - app - INFO - Generating XFWB XML...
2025-06-06 09:43:34,744 - xfwb_generator - INFO - Parsing input text: 706-51663054NBOLLW/T1K138.0MC1.01/HUMAN REMAINS
/HUM
706-60609942NBOLLW/T4K25.0MC0.01/DHL EXPRESS
/COU
2025-06-06 09:43:34,745 - xfwb_generator - INFO - Found AWB: 706-51663054
2025-06-06 09:43:34,745 - xfwb_generator - INFO - Added Handling Instructions codes to AWB 706-51663054: ['HUM']
2025-06-06 09:43:34,745 - xfwb_generator - INFO - Found AWB: 706-60609942
2025-06-06 09:43:34,745 - xfwb_generator - INFO - Added Handling Instructions codes to AWB 706-60609942: ['COU']
2025-06-06 09:43:34,745 - xfwb_generator - INFO - Parsed 2 AWBs
2025-06-06 09:43:34,745 - xfwb_generator - INFO - Generating consolidated XML for 2 AWBs
2025-06-06 09:43:34,745 - xfwb_generator - INFO - Processing AWB 1/2: 706-51663054
2025-06-06 09:43:34,746 - xfwb_generator - INFO - Processing AWB 2/2: 706-60609942
2025-06-06 09:43:34,747 - app - INFO - XFWB XML generated successfully, length: 9452
2025-06-06 09:43:34,747 - app - INFO - Multiple AWBs detected in the response
2025-06-06 09:43:34,747 - app - INFO - Found 2 AWBs in the response
2025-06-06 09:43:36,307 - app - INFO - Received XFWB XML generation request
2025-06-06 09:43:36,307 - app - INFO - Input text length: 52
2025-06-06 09:43:36,307 - app - INFO - Generating XFWB XML...
2025-06-06 09:43:36,308 - xfwb_generator - INFO - Parsing input text: 706-51663054NBOLLW/T1K138.0MC1.01/HUMAN REMAINS
/HUM
2025-06-06 09:43:36,308 - xfwb_generator - INFO - Found AWB: 706-51663054
2025-06-06 09:43:36,308 - xfwb_generator - INFO - Added Handling Instructions codes to AWB 706-51663054: ['HUM']
2025-06-06 09:43:36,308 - xfwb_generator - INFO - Parsed 1 AWBs
2025-06-06 09:43:36,309 - app - INFO - XFWB XML generated successfully, length: 4711
2025-06-06 09:43:36,309 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 09:43:36] "POST /api/generate-xfwb HTTP/1.1" 200 -
2025-06-06 09:43:48,812 - app - INFO - Serving static file: xfzb.html
2025-06-06 09:43:50,742 - app - INFO - Serving static file: api/sample-xfzb
2025-06-06 09:46:26,306 - app - INFO - Serving static file: xfwb.html
2025-06-06 09:46:26,306 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 09:46:26] "GET /xfwb.html HTTP/1.1" 200 -
2025-06-06 09:46:32,223 - app - INFO - Serving static file: xfwb.html
2025-06-06 09:46:33,068 - app - INFO - Serving static file: index.html
2025-06-06 09:46:34,563 - app - INFO - Serving static file: xfwb.html
2025-06-06 09:46:35,528 - app - INFO - Serving static file: xfzb.html
2025-06-06 09:46:36,456 - app - INFO - Serving static file: index.html
2025-06-06 09:46:40,925 - app - INFO - Received Flight Manifest XML generation request
2025-06-06 09:46:40,926 - app - INFO - Input text length: 252
2025-06-06 09:46:40,926 - app - INFO - Generating Flight Manifest XML...
2025-06-06 09:46:40,926 - app - INFO - Processing 12 lines of input
2025-06-06 09:46:40,926 - app - INFO - Processing line: FFM/8
2025-06-06 09:46:40,926 - app - INFO - Processing line: 1/KQ756/28APR/NBO/5Y-FFE
2025-06-06 09:46:40,929 - app - INFO - Processing line: LLW
2025-06-06 09:46:40,929 - app - INFO - Processing line: 706-51634332DXBLLW/P3K111.0MC0.26T13/LAPTOPS LITHIUM
2025-06-06 09:46:40,929 - app - INFO - Found AWB line: 706-51634332DXBLLW/P3K111.0MC0.26T13/LAPTOPS LITHIUM
2025-06-06 09:46:40,930 - app - INFO - Adding AWB: {'prefix': '706', 'number': '51634332', 'origin': 'DXB', 'destination': 'LLW', 'split_code': 'P', 'pieces': '3', 'weight': '111.0', 'volume': '0.26', 'description': 'LAPTOPS LITHIUM', 'special_codes': [], 'uld': None}
2025-06-06 09:46:40,932 - app - INFO - Processing line: /ELI/GEN
2025-06-06 09:46:40,933 - app - INFO - Found special code line: /ELI/GEN
2025-06-06 09:46:40,933 - app - INFO - Processing line: 706-60497953BOMLLW/T2K55.0MC0.33/COURIER MATERIA
2025-06-06 09:46:40,933 - app - INFO - Found AWB line: 706-60497953BOMLLW/T2K55.0MC0.33/COURIER MATERIA
2025-06-06 09:46:40,933 - app - INFO - Adding AWB: {'prefix': '706', 'number': '60497953', 'origin': 'BOM', 'destination': 'LLW', 'split_code': 'T', 'pieces': '2', 'weight': '55.0', 'volume': '0.33', 'description': 'COURIER MATERIA', 'special_codes': [], 'uld': None}
2025-06-06 09:46:40,933 - app - INFO - Processing line: /COU
2025-06-06 09:46:40,933 - app - INFO - Found special code line: /COU
2025-06-06 09:46:40,933 - app - INFO - Processing line: 706-60606280NBOLLW/T2K2.0MC0.01/DHL EXPRESS
2025-06-06 09:46:40,934 - app - INFO - Found AWB line: 706-60606280NBOLLW/T2K2.0MC0.01/DHL EXPRESS
2025-06-06 09:46:40,934 - app - INFO - Adding AWB: {'prefix': '706', 'number': '60606280', 'origin': 'NBO', 'destination': 'LLW', 'split_code': 'T', 'pieces': '2', 'weight': '2.0', 'volume': '0.01', 'description': 'DHL EXPRESS', 'special_codes': [], 'uld': None}
2025-06-06 09:46:40,934 - app - INFO - Processing line: /COU
2025-06-06 09:46:40,934 - app - INFO - Found special code line: /COU
2025-06-06 09:46:40,935 - app - INFO - Processing line: 706-41026672DXBLLW/T1K7.0MC0.13/COURIER
2025-06-06 09:46:40,936 - app - INFO - Found AWB line: 706-41026672DXBLLW/T1K7.0MC0.13/COURIER
2025-06-06 09:46:40,936 - app - INFO - Adding AWB: {'prefix': '706', 'number': '41026672', 'origin': 'DXB', 'destination': 'LLW', 'split_code': 'T', 'pieces': '1', 'weight': '7.0', 'volume': '0.13', 'description': 'COURIER', 'special_codes': [], 'uld': None}
2025-06-06 09:46:40,936 - app - INFO - Processing line: /COU
2025-06-06 09:46:40,936 - app - INFO - Found special code line: /COU
2025-06-06 09:46:40,937 - app - INFO - Processing line: APL/NIL
2025-06-06 09:46:40,937 - app - INFO - Found 4 AWBs in the input
2025-06-06 09:46:40,937 - app - INFO - Found flight line: 1/KQ756/28APR/NBO/5Y-FFE
2025-06-06 09:46:40,937 - app - INFO - Found arrival airport: LLW
2025-06-06 09:46:40,937 - app - INFO - Flight details: KQ756, 28APR, NBO, LLW, 5Y-FFE
2025-06-06 09:46:40,937 - app - INFO - Formatted departure date: 2025-04-28T00:00:00
2025-06-06 09:46:40,938 - app - INFO - Generating XML for ULD: PMC01921KQ
2025-06-06 09:46:40,940 - app - INFO - Adding AWB to ULD 0: 706-51634332
2025-06-06 09:46:40,940 - app - INFO - Adding AWB to ULD 0: 706-60497953
2025-06-06 09:46:40,940 - app - INFO - Adding AWB to ULD 0: 706-60606280
2025-06-06 09:46:40,940 - app - INFO - Adding AWB to ULD 0: 706-41026672
2025-06-06 09:46:40,940 - app - INFO - XML generated successfully, length: 6338
2025-06-06 09:59:15,556 - app - INFO - Serving static file: xfwb.html
2025-06-06 09:59:15,556 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 09:59:15] "[36mGET /xfwb.html HTTP/1.1[0m" 304 -
2025-06-06 09:59:24,428 - app - INFO - XFWB sample data requested
2025-06-06 09:59:24,429 - app - INFO - XFWB sample data sent
2025-06-06 09:59:24,429 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 09:59:24] "GET /api/sample-xfwb HTTP/1.1" 200 -
2025-06-06 09:59:37,467 - app - INFO - Serving static file: xfwb.html
2025-06-06 09:59:39,822 - app - INFO - XFWB sample data requested
2025-06-06 09:59:39,822 - app - INFO - XFWB sample data sent
2025-06-06 09:59:41,978 - app - INFO - Received XFWB XML generation request
2025-06-06 09:59:41,979 - app - INFO - Input text length: 52
2025-06-06 09:59:41,979 - app - INFO - Generating XFWB XML...
2025-06-06 09:59:41,979 - xfwb_generator - INFO - Parsing input text: 706-51663054NBOLLW/T1K138.0MC1.01/HUMAN REMAINS
/HUM
2025-06-06 09:59:41,979 - xfwb_generator - INFO - Found AWB: 706-51663054
2025-06-06 09:59:41,979 - xfwb_generator - INFO - Added Handling Instructions codes to AWB 706-51663054: ['HUM']
2025-06-06 09:59:41,980 - xfwb_generator - INFO - Parsed 1 AWBs
2025-06-06 09:59:41,980 - app - INFO - XFWB XML generated successfully, length: 4711
2025-06-06 09:59:41,980 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 09:59:41] "POST /api/generate-xfwb HTTP/1.1" 200 -
2025-06-06 09:59:47,786 - app - INFO - Received XFWB XML generation request
2025-06-06 09:59:47,787 - app - INFO - Input text length: 52
2025-06-06 09:59:47,787 - app - INFO - Generating XFWB XML...
2025-06-06 09:59:47,787 - xfwb_generator - INFO - Parsing input text: 706-51663054NBOLLW/T1K138.0MC1.01/HUMAN REMAINS
/HUM
2025-06-06 09:59:47,787 - xfwb_generator - INFO - Found AWB: 706-51663054
2025-06-06 09:59:47,787 - xfwb_generator - INFO - Added Handling Instructions codes to AWB 706-51663054: ['HUM']
2025-06-06 09:59:47,787 - xfwb_generator - INFO - Parsed 1 AWBs
2025-06-06 09:59:47,788 - app - INFO - XFWB XML generated successfully, length: 4711
2025-06-06 10:00:09,139 - app - INFO - Serving static file: xfzb.html
2025-06-06 10:00:22,465 - app - INFO - Serving static file: api/sample-xfzb
2025-06-06 10:00:30,734 - app - INFO - Serving static file: api/sample-xfzb
2025-06-06 10:01:33,764 - app - INFO - Serving static file: xfzb.html
2025-06-06 10:01:33,765 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 10:01:33] "[36mGET /xfzb.html HTTP/1.1[0m" 304 -
2025-06-06 10:14:28,376 - app - INFO - XFZB sample data requested
2025-06-06 10:14:28,376 - app - INFO - XFZB sample data sent
2025-06-06 10:14:28,379 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 10:14:28] "GET /api/sample-xfzb HTTP/1.1" 200 -
2025-06-06 10:14:38,254 - app - INFO - Received XFZB XML generation request
2025-06-06 10:14:38,254 - app - INFO - Input text length: 40
2025-06-06 10:14:38,254 - app - INFO - Generating XFZB XML...
2025-06-06 10:14:38,254 - xfzb_generator - INFO - Parsing input text: SHAS51282599/T1K4.0MC0.027/panel pc
/EAW
2025-06-06 10:14:38,254 - xfzb_generator - INFO - Found House AWB: SHAS51282599
2025-06-06 10:14:38,254 - xfzb_generator - INFO - Added Handling Instructions codes to House AWB SHAS51282599: ['EAW']
2025-06-06 10:14:38,254 - xfzb_generator - INFO - Parsed 1 House AWBs
2025-06-06 10:14:38,256 - app - INFO - XFZB XML generated successfully, length: 6168
2025-06-06 10:14:38,256 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 10:14:38] "POST /api/generate-xfzb HTTP/1.1" 200 -
2025-06-06 10:16:12,764 - app - INFO - Received XFWB XML generation request
2025-06-06 10:16:12,766 - app - INFO - Input text length: 0
2025-06-06 10:16:12,766 - app - WARNING - No input text provided
2025-06-06 10:16:12,766 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 10:16:12] "[31m[1mPOST /api/generate-xfwb HTTP/1.1[0m" 400 -
2025-06-06 10:20:45,032 - werkzeug - INFO -  * Detected change in '/var/www/xmlmaker/backend/app.py', reloading
2025-06-06 10:20:45,065 - werkzeug - INFO -  * Restarting with stat
2025-06-06 10:20:45,242 - app - INFO - IATA XML Generator startup
2025-06-06 10:20:45,242 - app - INFO - Static folder path: /var/www/xmlmaker/frontend/static
2025-06-06 10:20:45,247 - app - INFO - Starting server on port 5002
2025-06-06 10:20:45,251 - werkzeug - WARNING -  * Debugger is active!
2025-06-06 10:20:45,255 - werkzeug - INFO -  * Debugger PIN: 990-968-340
2025-06-06 10:21:36,331 - werkzeug - INFO -  * Detected change in '/var/www/xmlmaker/backend/app.py', reloading
2025-06-06 10:21:36,431 - werkzeug - INFO -  * Restarting with stat
2025-06-06 10:21:36,655 - app - INFO - IATA XML Generator startup
2025-06-06 10:21:36,656 - app - INFO - Static folder path: /var/www/xmlmaker/frontend/static
2025-06-06 10:21:36,660 - app - INFO - Starting server on port 5002
2025-06-06 10:21:36,663 - werkzeug - WARNING -  * Debugger is active!
2025-06-06 10:21:36,666 - werkzeug - INFO -  * Debugger PIN: 990-968-340
2025-06-06 10:23:30,884 - app - INFO - Serving static file: xfzb.html
2025-06-06 10:23:34,361 - app - INFO - Serving static file: api/sample-xfzb
2025-06-06 10:27:42,111 - app - INFO - Random cargo data requested
2025-06-06 10:27:42,111 - app - INFO - Random cargo data generated successfully
2025-06-06 10:27:42,112 - werkzeug - INFO - 127.0.0.1 - - [06/Jun/2025 10:27:42] "GET /api/random-data HTTP/1.1" 200 -
2025-06-06 10:53:32,400 - app - INFO - Serving index.html
2025-06-06 10:53:34,927 - app - INFO - Sample data requested
2025-06-06 10:53:34,927 - app - INFO - Sample data sent
2025-06-06 10:56:35,363 - app - INFO - Received Flight Manifest XML generation request
2025-06-06 10:56:35,364 - app - INFO - Input text length: 558
2025-06-06 10:56:35,364 - app - INFO - Generating Flight Manifest XML...
2025-06-06 10:56:35,364 - app - INFO - Processing 24 lines of input
2025-06-06 10:56:35,364 - app - INFO - Processing line: FFM/8
2025-06-06 10:56:35,365 - app - INFO - Processing line: 1/KQ756/28APR/NBO/5Y-FFE
2025-06-06 10:56:35,365 - app - INFO - Processing line: LLW
2025-06-06 10:56:35,365 - app - INFO - Processing line: 706-51634332DXBLLW/P1K90.0MC0.50/HUMAN REMAINS
2025-06-06 10:56:35,365 - app - INFO - Found AWB line: 706-51634332DXBLLW/P1K90.0MC0.50/HUMAN REMAINS
2025-06-06 10:56:35,366 - app - INFO - Adding AWB: {'prefix': '706', 'number': '51634332', 'origin': 'DXB', 'destination': 'LLW', 'split_code': 'P', 'pieces': '1', 'weight': '90.0', 'volume': '0.50', 'description': 'HUMAN REMAINS', 'special_codes': [], 'uld': None}
2025-06-06 10:56:35,366 - app - INFO - Processing line: /HUM
2025-06-06 10:56:35,366 - app - INFO - Found special code line: /HUM
2025-06-06 10:56:35,366 - app - INFO - Processing line: 706-60497953BOMLLW/P5K100.0MC0.60/COURIER DHL
2025-06-06 10:56:35,366 - app - INFO - Found AWB line: 706-60497953BOMLLW/P5K100.0MC0.60/COURIER DHL
2025-06-06 10:56:35,366 - app - INFO - Adding AWB: {'prefix': '706', 'number': '60497953', 'origin': 'BOM', 'destination': 'LLW', 'split_code': 'P', 'pieces': '5', 'weight': '100.0', 'volume': '0.60', 'description': 'COURIER DHL', 'special_codes': [], 'uld': None}
2025-06-06 10:56:35,367 - app - INFO - Processing line: /COU
2025-06-06 10:56:35,367 - app - INFO - Found special code line: /COU
2025-06-06 10:56:35,367 - app - INFO - Processing line: 706-60606280NBOLLW/P1K5.0MC0.03/DOG
2025-06-06 10:56:35,367 - app - INFO - Found AWB line: 706-60606280NBOLLW/P1K5.0MC0.03/DOG
2025-06-06 10:56:35,367 - app - INFO - Adding AWB: {'prefix': '706', 'number': '60606280', 'origin': 'NBO', 'destination': 'LLW', 'split_code': 'P', 'pieces': '1', 'weight': '5.0', 'volume': '0.03', 'description': 'DOG', 'special_codes': [], 'uld': None}
2025-06-06 10:56:35,367 - app - INFO - Processing line: /AVI
2025-06-06 10:56:35,367 - app - INFO - Found special code line: /AVI
2025-06-06 10:56:35,367 - app - INFO - Processing line: 706-41026672DXBLLW/P1K5.0MC0.03/MEDICAL DRUGS
2025-06-06 10:56:35,368 - app - INFO - Found AWB line: 706-41026672DXBLLW/P1K5.0MC0.03/MEDICAL DRUGS
2025-06-06 10:56:35,368 - app - INFO - Adding AWB: {'prefix': '706', 'number': '41026672', 'origin': 'DXB', 'destination': 'LLW', 'split_code': 'P', 'pieces': '1', 'weight': '5.0', 'volume': '0.03', 'description': 'MEDICAL DRUGS', 'special_codes': [], 'uld': None}
2025-06-06 10:56:35,368 - app - INFO - Processing line: /PHI
2025-06-06 10:56:35,368 - app - INFO - Found special code line: /PHI
2025-06-06 10:56:35,368 - app - INFO - Processing line: 706-00000001DXBLLW/P10K500.0MC2.50/LITHIUM BATTERIES
2025-06-06 10:56:35,369 - app - INFO - Found AWB line: 706-00000001DXBLLW/P10K500.0MC2.50/LITHIUM BATTERIES
2025-06-06 10:56:35,369 - app - INFO - Adding AWB: {'prefix': '706', 'number': '00000001', 'origin': 'DXB', 'destination': 'LLW', 'split_code': 'P', 'pieces': '10', 'weight': '500.0', 'volume': '2.50', 'description': 'LITHIUM BATTERIES', 'special_codes': [], 'uld': None}
2025-06-06 10:56:35,369 - app - INFO - Processing line: /ELI
2025-06-06 10:56:35,369 - app - INFO - Found special code line: /ELI
2025-06-06 10:56:35,369 - app - INFO - Processing line: 706-00000002DXBLLW/P1K10.0MC0.06/CLOTHES
2025-06-06 10:56:35,370 - app - INFO - Found AWB line: 706-00000002DXBLLW/P1K10.0MC0.06/CLOTHES
2025-06-06 10:56:35,370 - app - INFO - Adding AWB: {'prefix': '706', 'number': '00000002', 'origin': 'DXB', 'destination': 'LLW', 'split_code': 'P', 'pieces': '1', 'weight': '10.0', 'volume': '0.06', 'description': 'CLOTHES', 'special_codes': [], 'uld': None}
2025-06-06 10:56:35,370 - app - INFO - Processing line: /GEN
2025-06-06 10:56:35,370 - app - INFO - Found special code line: /GEN
2025-06-06 10:56:35,370 - app - INFO - Processing line: 706-00000003DXBLLW/T1K200.0MC1.00/CONSOLIDATED SHIPMENT
2025-06-06 10:56:35,371 - app - INFO - Found AWB line: 706-00000003DXBLLW/T1K200.0MC1.00/CONSOLIDATED SHIPMENT
2025-06-06 10:56:35,371 - app - INFO - Adding AWB: {'prefix': '706', 'number': '00000003', 'origin': 'DXB', 'destination': 'LLW', 'split_code': 'T', 'pieces': '1', 'weight': '200.0', 'volume': '1.00', 'description': 'CONSOLIDATED SHIPMENT', 'special_codes': [], 'uld': None}
2025-06-06 10:56:35,371 - app - INFO - Processing line: /GEN
2025-06-06 10:56:35,371 - app - INFO - Found special code line: /GEN
2025-06-06 10:56:35,371 - app - INFO - Processing line: 706-00000004DXBLLW/T100K234.4MC1.17/MAIL
2025-06-06 10:56:35,371 - app - INFO - Found AWB line: 706-00000004DXBLLW/T100K234.4MC1.17/MAIL
2025-06-06 10:56:35,371 - app - INFO - Adding AWB: {'prefix': '706', 'number': '00000004', 'origin': 'DXB', 'destination': 'LLW', 'split_code': 'T', 'pieces': '100', 'weight': '234.4', 'volume': '1.17', 'description': 'MAIL', 'special_codes': [], 'uld': None}
2025-06-06 10:56:35,372 - app - INFO - Processing line: /MAIL
2025-06-06 10:56:35,372 - app - INFO - Found special code line: /MAIL
2025-06-06 10:56:35,372 - app - INFO - Processing line: 706-00000005DXBLLW/P50K354.0MC1.77/LAPTOPS
2025-06-06 10:56:35,372 - app - INFO - Found AWB line: 706-00000005DXBLLW/P50K354.0MC1.77/LAPTOPS
2025-06-06 10:56:35,372 - app - INFO - Adding AWB: {'prefix': '706', 'number': '00000005', 'origin': 'DXB', 'destination': 'LLW', 'split_code': 'P', 'pieces': '50', 'weight': '354.0', 'volume': '1.77', 'description': 'LAPTOPS', 'special_codes': [], 'uld': None}
2025-06-06 10:56:35,372 - app - INFO - Processing line: /GEN
2025-06-06 10:56:35,373 - app - INFO - Found special code line: /GEN
2025-06-06 10:56:35,373 - app - INFO - Processing line: 706-00000006DXBLLW/T40K9564.0MC47.82/CONSOLIDATED CARGO
2025-06-06 10:56:35,373 - app - INFO - Found AWB line: 706-00000006DXBLLW/T40K9564.0MC47.82/CONSOLIDATED CARGO
2025-06-06 10:56:35,373 - app - INFO - Adding AWB: {'prefix': '706', 'number': '00000006', 'origin': 'DXB', 'destination': 'LLW', 'split_code': 'T', 'pieces': '40', 'weight': '9564.0', 'volume': '47.82', 'description': 'CONSOLIDATED CARGO', 'special_codes': [], 'uld': None}
2025-06-06 10:56:35,373 - app - INFO - Processing line: /GEN
2025-06-06 10:56:35,373 - app - INFO - Found special code line: /GEN
2025-06-06 10:56:35,373 - app - INFO - Processing line: APL/NIL
2025-06-06 10:56:35,374 - app - INFO - Found 10 AWBs in the input
2025-06-06 10:56:35,374 - app - INFO - Found flight line: 1/KQ756/28APR/NBO/5Y-FFE
2025-06-06 10:56:35,374 - app - INFO - Found arrival airport: LLW
2025-06-06 10:56:35,374 - app - INFO - Flight details: KQ756, 28APR, NBO, LLW, 5Y-FFE
2025-06-06 10:56:35,375 - app - INFO - Formatted departure date: 2025-04-28T00:00:00
2025-06-06 10:56:35,375 - app - INFO - Generating XML for ULD: PMC01921KQ
2025-06-06 10:56:35,375 - app - INFO - Adding AWB to ULD 0: 706-51634332
2025-06-06 10:56:35,375 - app - INFO - Adding AWB to ULD 0: 706-60497953
2025-06-06 10:56:35,375 - app - INFO - Adding AWB to ULD 0: 706-60606280
2025-06-06 10:56:35,375 - app - INFO - Adding AWB to ULD 0: 706-41026672
2025-06-06 10:56:35,375 - app - INFO - Adding AWB to ULD 0: 706-00000001
2025-06-06 10:56:35,375 - app - INFO - Adding AWB to ULD 0: 706-00000002
2025-06-06 10:56:35,376 - app - INFO - Adding AWB to ULD 0: 706-00000003
2025-06-06 10:56:35,376 - app - INFO - Adding AWB to ULD 0: 706-00000004
2025-06-06 10:56:35,376 - app - INFO - Adding AWB to ULD 0: 706-00000005
2025-06-06 10:56:35,376 - app - INFO - Adding AWB to ULD 0: 706-00000006
2025-06-06 10:56:35,376 - app - INFO - XML generated successfully, length: 11535
2025-06-06 10:57:37,565 - app - INFO - Received Flight Manifest XML generation request
2025-06-06 10:57:37,565 - app - INFO - Input text length: 558
2025-06-06 10:57:37,565 - app - INFO - Generating Flight Manifest XML...
2025-06-06 10:57:37,565 - app - INFO - Processing 24 lines of input
2025-06-06 10:57:37,565 - app - INFO - Processing line: FFM/8
2025-06-06 10:57:37,565 - app - INFO - Processing line: 1/EK9951/06JUN/NBOEK-FFM
2025-06-06 10:57:37,566 - app - INFO - Processing line: LLW
2025-06-06 10:57:37,566 - app - INFO - Processing line: 706-51634332DXBLLW/P1K90.0MC0.50/HUMAN REMAINS
2025-06-06 10:57:37,566 - app - INFO - Found AWB line: 706-51634332DXBLLW/P1K90.0MC0.50/HUMAN REMAINS
2025-06-06 10:57:37,566 - app - INFO - Adding AWB: {'prefix': '706', 'number': '51634332', 'origin': 'DXB', 'destination': 'LLW', 'split_code': 'P', 'pieces': '1', 'weight': '90.0', 'volume': '0.50', 'description': 'HUMAN REMAINS', 'special_codes': [], 'uld': None}
2025-06-06 10:57:37,566 - app - INFO - Processing line: /HUM
2025-06-06 10:57:37,566 - app - INFO - Found special code line: /HUM
2025-06-06 10:57:37,566 - app - INFO - Processing line: 706-60497953BOMLLW/P5K100.0MC0.60/COURIER DHL
2025-06-06 10:57:37,566 - app - INFO - Found AWB line: 706-60497953BOMLLW/P5K100.0MC0.60/COURIER DHL
2025-06-06 10:57:37,567 - app - INFO - Adding AWB: {'prefix': '706', 'number': '60497953', 'origin': 'BOM', 'destination': 'LLW', 'split_code': 'P', 'pieces': '5', 'weight': '100.0', 'volume': '0.60', 'description': 'COURIER DHL', 'special_codes': [], 'uld': None}
2025-06-06 10:57:37,567 - app - INFO - Processing line: /COU
2025-06-06 10:57:37,567 - app - INFO - Found special code line: /COU
2025-06-06 10:57:37,567 - app - INFO - Processing line: 706-60606280NBOLLW/P1K5.0MC0.03/DOG
2025-06-06 10:57:37,567 - app - INFO - Found AWB line: 706-60606280NBOLLW/P1K5.0MC0.03/DOG
2025-06-06 10:57:37,567 - app - INFO - Adding AWB: {'prefix': '706', 'number': '60606280', 'origin': 'NBO', 'destination': 'LLW', 'split_code': 'P', 'pieces': '1', 'weight': '5.0', 'volume': '0.03', 'description': 'DOG', 'special_codes': [], 'uld': None}
2025-06-06 10:57:37,567 - app - INFO - Processing line: /AVI
2025-06-06 10:57:37,567 - app - INFO - Found special code line: /AVI
2025-06-06 10:57:37,568 - app - INFO - Processing line: 706-41026672DXBLLW/P1K5.0MC0.03/MEDICAL DRUGS
2025-06-06 10:57:37,568 - app - INFO - Found AWB line: 706-41026672DXBLLW/P1K5.0MC0.03/MEDICAL DRUGS
2025-06-06 10:57:37,568 - app - INFO - Adding AWB: {'prefix': '706', 'number': '41026672', 'origin': 'DXB', 'destination': 'LLW', 'split_code': 'P', 'pieces': '1', 'weight': '5.0', 'volume': '0.03', 'description': 'MEDICAL DRUGS', 'special_codes': [], 'uld': None}
2025-06-06 10:57:37,569 - app - INFO - Processing line: /PHI
2025-06-06 10:57:37,569 - app - INFO - Found special code line: /PHI
2025-06-06 10:57:37,569 - app - INFO - Processing line: 706-00000001DXBLLW/P10K500.0MC2.50/LITHIUM BATTERIES
2025-06-06 10:57:37,569 - app - INFO - Found AWB line: 706-00000001DXBLLW/P10K500.0MC2.50/LITHIUM BATTERIES
2025-06-06 10:57:37,569 - app - INFO - Adding AWB: {'prefix': '706', 'number': '00000001', 'origin': 'DXB', 'destination': 'LLW', 'split_code': 'P', 'pieces': '10', 'weight': '500.0', 'volume': '2.50', 'description': 'LITHIUM BATTERIES', 'special_codes': [], 'uld': None}
2025-06-06 10:57:37,569 - app - INFO - Processing line: /ELI
2025-06-06 10:57:37,569 - app - INFO - Found special code line: /ELI
2025-06-06 10:57:37,569 - app - INFO - Processing line: 706-00000002DXBLLW/P1K10.0MC0.06/CLOTHES
2025-06-06 10:57:37,569 - app - INFO - Found AWB line: 706-00000002DXBLLW/P1K10.0MC0.06/CLOTHES
2025-06-06 10:57:37,570 - app - INFO - Adding AWB: {'prefix': '706', 'number': '00000002', 'origin': 'DXB', 'destination': 'LLW', 'split_code': 'P', 'pieces': '1', 'weight': '10.0', 'volume': '0.06', 'description': 'CLOTHES', 'special_codes': [], 'uld': None}
2025-06-06 10:57:37,570 - app - INFO - Processing line: /GEN
2025-06-06 10:57:37,570 - app - INFO - Found special code line: /GEN
2025-06-06 10:57:37,570 - app - INFO - Processing line: 706-00000003DXBLLW/T1K200.0MC1.00/CONSOLIDATED SHIPMENT
2025-06-06 10:57:37,570 - app - INFO - Found AWB line: 706-00000003DXBLLW/T1K200.0MC1.00/CONSOLIDATED SHIPMENT
2025-06-06 10:57:37,571 - app - INFO - Adding AWB: {'prefix': '706', 'number': '00000003', 'origin': 'DXB', 'destination': 'LLW', 'split_code': 'T', 'pieces': '1', 'weight': '200.0', 'volume': '1.00', 'description': 'CONSOLIDATED SHIPMENT', 'special_codes': [], 'uld': None}
2025-06-06 10:57:37,571 - app - INFO - Processing line: /GEN
2025-06-06 10:57:37,571 - app - INFO - Found special code line: /GEN
2025-06-06 10:57:37,571 - app - INFO - Processing line: 706-00000004DXBLLW/T100K234.4MC1.17/MAIL
2025-06-06 10:57:37,571 - app - INFO - Found AWB line: 706-00000004DXBLLW/T100K234.4MC1.17/MAIL
2025-06-06 10:57:37,571 - app - INFO - Adding AWB: {'prefix': '706', 'number': '00000004', 'origin': 'DXB', 'destination': 'LLW', 'split_code': 'T', 'pieces': '100', 'weight': '234.4', 'volume': '1.17', 'description': 'MAIL', 'special_codes': [], 'uld': None}
2025-06-06 10:57:37,571 - app - INFO - Processing line: /MAIL
2025-06-06 10:57:37,572 - app - INFO - Found special code line: /MAIL
2025-06-06 10:57:37,572 - app - INFO - Processing line: 706-00000005DXBLLW/P50K354.0MC1.77/LAPTOPS
2025-06-06 10:57:37,572 - app - INFO - Found AWB line: 706-00000005DXBLLW/P50K354.0MC1.77/LAPTOPS
2025-06-06 10:57:37,572 - app - INFO - Adding AWB: {'prefix': '706', 'number': '00000005', 'origin': 'DXB', 'destination': 'LLW', 'split_code': 'P', 'pieces': '50', 'weight': '354.0', 'volume': '1.77', 'description': 'LAPTOPS', 'special_codes': [], 'uld': None}
2025-06-06 10:57:37,572 - app - INFO - Processing line: /GEN
2025-06-06 10:57:37,572 - app - INFO - Found special code line: /GEN
2025-06-06 10:57:37,572 - app - INFO - Processing line: 706-00000006DXBLLW/T40K9564.0MC47.82/CONSOLIDATED CARGO
2025-06-06 10:57:37,572 - app - INFO - Found AWB line: 706-00000006DXBLLW/T40K9564.0MC47.82/CONSOLIDATED CARGO
2025-06-06 10:57:37,572 - app - INFO - Adding AWB: {'prefix': '706', 'number': '00000006', 'origin': 'DXB', 'destination': 'LLW', 'split_code': 'T', 'pieces': '40', 'weight': '9564.0', 'volume': '47.82', 'description': 'CONSOLIDATED CARGO', 'special_codes': [], 'uld': None}
2025-06-06 10:57:37,572 - app - INFO - Processing line: /GEN
2025-06-06 10:57:37,572 - app - INFO - Found special code line: /GEN
2025-06-06 10:57:37,573 - app - INFO - Processing line: APL/NIL
2025-06-06 10:57:37,573 - app - INFO - Found 10 AWBs in the input
2025-06-06 10:57:37,574 - app - INFO - Found arrival airport: LLW
2025-06-06 10:57:37,574 - app - INFO - Flight details: EK9747, 09MAY0820, DWC, LLW, A6-EFL
2025-06-06 10:57:37,574 - app - INFO - Formatted departure date: 2025-05-09T08:20:00
2025-06-06 10:57:37,574 - app - INFO - Generating XML for ULD: PMC01921EK
2025-06-06 10:57:37,574 - app - INFO - Adding AWB to ULD 0: 706-51634332
2025-06-06 10:57:37,575 - app - INFO - Adding AWB to ULD 0: 706-60497953
2025-06-06 10:57:37,575 - app - INFO - Adding AWB to ULD 0: 706-60606280
2025-06-06 10:57:37,575 - app - INFO - Adding AWB to ULD 0: 706-41026672
2025-06-06 10:57:37,575 - app - INFO - Adding AWB to ULD 0: 706-00000001
2025-06-06 10:57:37,575 - app - INFO - Adding AWB to ULD 0: 706-00000002
2025-06-06 10:57:37,575 - app - INFO - Adding AWB to ULD 0: 706-00000003
2025-06-06 10:57:37,576 - app - INFO - Adding AWB to ULD 0: 706-00000004
2025-06-06 10:57:37,576 - app - INFO - Adding AWB to ULD 0: 706-00000005
2025-06-06 10:57:37,576 - app - INFO - Adding AWB to ULD 0: 706-00000006
2025-06-06 10:57:37,576 - app - INFO - XML generated successfully, length: 11546
2025-06-06 11:50:12,456 - app - INFO - Serving index.html
2025-06-06 11:50:14,062 - app - INFO - Serving static file: favicon.ico
2025-06-06 11:50:16,847 - app - INFO - Serving static file: xfwb.html
2025-06-06 11:50:19,184 - app - INFO - XFWB sample data requested
2025-06-06 11:50:19,184 - app - INFO - XFWB sample data sent
2025-06-06 11:50:27,670 - app - INFO - Received XFWB XML generation request
2025-06-06 11:50:27,671 - app - INFO - Input text length: 52
2025-06-06 11:50:27,671 - app - INFO - Generating XFWB XML...
2025-06-06 11:50:27,671 - xfwb_generator - INFO - Parsing input text: 706-51663054NBOLLW/T1K138.0MC1.01/HUMAN REMAINS
/HUM
2025-06-06 11:50:27,671 - xfwb_generator - INFO - Found AWB: 706-51663054
2025-06-06 11:50:27,671 - xfwb_generator - INFO - Added Handling Instructions codes to AWB 706-51663054: ['HUM']
2025-06-06 11:50:27,671 - xfwb_generator - INFO - Parsed 1 AWBs
2025-06-06 11:50:27,672 - app - INFO - XFWB XML generated successfully, length: 4711
2025-06-06 11:53:38,899 - app - INFO - Serving static file: xfzb.html
2025-06-06 11:53:40,111 - app - INFO - Serving static file: index.html
2025-06-06 11:53:41,119 - app - INFO - Serving static file: index.html
2025-06-06 11:54:22,680 - app - INFO - Sample data requested
2025-06-06 11:54:22,680 - app - INFO - Sample data sent
2025-06-06 11:55:05,516 - app - INFO - Received Flight Manifest XML generation request
2025-06-06 11:55:05,516 - app - INFO - Input text length: 252
2025-06-06 11:55:05,516 - app - INFO - Generating Flight Manifest XML...
2025-06-06 11:55:05,517 - app - INFO - Processing 12 lines of input
2025-06-06 11:55:05,517 - app - INFO - Processing line: FFM/8
2025-06-06 11:55:05,517 - app - INFO - Processing line: 1/KQ756/06JUN/NBO/5Y-FFE
2025-06-06 11:55:05,517 - app - INFO - Processing line: LLW
2025-06-06 11:55:05,517 - app - INFO - Processing line: 706-51634332DXBLLW/P3K111.0MC0.26T13/LAPTOPS LITHIUM
2025-06-06 11:55:05,517 - app - INFO - Found AWB line: 706-51634332DXBLLW/P3K111.0MC0.26T13/LAPTOPS LITHIUM
2025-06-06 11:55:05,518 - app - INFO - Adding AWB: {'prefix': '706', 'number': '51634332', 'origin': 'DXB', 'destination': 'LLW', 'split_code': 'P', 'pieces': '3', 'weight': '111.0', 'volume': '0.26', 'description': 'LAPTOPS LITHIUM', 'special_codes': [], 'uld': None}
2025-06-06 11:55:05,518 - app - INFO - Processing line: /ELI/GEN
2025-06-06 11:55:05,518 - app - INFO - Found special code line: /ELI/GEN
2025-06-06 11:55:05,518 - app - INFO - Processing line: 706-60497953BOMLLW/T2K55.0MC0.33/COURIER MATERIA
2025-06-06 11:55:05,518 - app - INFO - Found AWB line: 706-60497953BOMLLW/T2K55.0MC0.33/COURIER MATERIA
2025-06-06 11:55:05,518 - app - INFO - Adding AWB: {'prefix': '706', 'number': '60497953', 'origin': 'BOM', 'destination': 'LLW', 'split_code': 'T', 'pieces': '2', 'weight': '55.0', 'volume': '0.33', 'description': 'COURIER MATERIA', 'special_codes': [], 'uld': None}
2025-06-06 11:55:05,518 - app - INFO - Processing line: /COU
2025-06-06 11:55:05,518 - app - INFO - Found special code line: /COU
2025-06-06 11:55:05,518 - app - INFO - Processing line: 706-60606280NBOLLW/T2K2.0MC0.01/DHL EXPRESS
2025-06-06 11:55:05,518 - app - INFO - Found AWB line: 706-60606280NBOLLW/T2K2.0MC0.01/DHL EXPRESS
2025-06-06 11:55:05,518 - app - INFO - Adding AWB: {'prefix': '706', 'number': '60606280', 'origin': 'NBO', 'destination': 'LLW', 'split_code': 'T', 'pieces': '2', 'weight': '2.0', 'volume': '0.01', 'description': 'DHL EXPRESS', 'special_codes': [], 'uld': None}
2025-06-06 11:55:05,518 - app - INFO - Processing line: /COU
2025-06-06 11:55:05,519 - app - INFO - Found special code line: /COU
2025-06-06 11:55:05,519 - app - INFO - Processing line: 706-41026672DXBLLW/T1K7.0MC0.13/COURIER
2025-06-06 11:55:05,519 - app - INFO - Found AWB line: 706-41026672DXBLLW/T1K7.0MC0.13/COURIER
2025-06-06 11:55:05,519 - app - INFO - Adding AWB: {'prefix': '706', 'number': '41026672', 'origin': 'DXB', 'destination': 'LLW', 'split_code': 'T', 'pieces': '1', 'weight': '7.0', 'volume': '0.13', 'description': 'COURIER', 'special_codes': [], 'uld': None}
2025-06-06 11:55:05,519 - app - INFO - Processing line: /COU
2025-06-06 11:55:05,519 - app - INFO - Found special code line: /COU
2025-06-06 11:55:05,519 - app - INFO - Processing line: APL/NIL
2025-06-06 11:55:05,519 - app - INFO - Found 4 AWBs in the input
2025-06-06 11:55:05,520 - app - INFO - Found flight line: 1/KQ756/06JUN/NBO/5Y-FFE
2025-06-06 11:55:05,520 - app - INFO - Found arrival airport: LLW
2025-06-06 11:55:05,520 - app - INFO - Flight details: KQ756, 06JUN, NBO, LLW, 5Y-FFE
2025-06-06 11:55:05,520 - app - INFO - Formatted departure date: 2025-06-06T00:00:00
2025-06-06 11:55:05,520 - app - INFO - Generating XML for ULD: PMC01921KQ
2025-06-06 11:55:05,520 - app - INFO - Adding AWB to ULD 0: 706-51634332
2025-06-06 11:55:05,521 - app - INFO - Adding AWB to ULD 0: 706-60497953
2025-06-06 11:55:05,521 - app - INFO - Adding AWB to ULD 0: 706-60606280
2025-06-06 11:55:05,521 - app - INFO - Adding AWB to ULD 0: 706-41026672
2025-06-06 11:55:05,521 - app - INFO - XML generated successfully, length: 6338
2025-06-06 12:00:41,592 - app - INFO - Serving static file: xfwb.html
2025-06-06 12:00:51,629 - app - INFO - Received XFWB XML generation request
2025-06-06 12:00:51,630 - app - INFO - Input text length: 61
2025-06-06 12:00:51,631 - app - INFO - Generating XFWB XML...
2025-06-06 12:00:51,633 - xfwb_generator - INFO - Parsing input text: 706-51634332DXBLLW/P3K111.0MC0.26T13/LAPTOPS LITHIUM
/ELI/GEN
2025-06-06 12:00:51,634 - xfwb_generator - WARNING - No valid AWBs found in the input text
2025-06-06 12:00:51,634 - xfwb_generator - WARNING - No AWBs found to generate XML
2025-06-06 12:00:51,634 - app - ERROR - Error in XFWB generation: No valid AWBs found to generate XML
2025-06-06 12:00:57,969 - app - INFO - XFWB sample data requested
2025-06-06 12:00:57,969 - app - INFO - XFWB sample data sent
2025-06-06 12:01:05,348 - app - INFO - Received XFWB XML generation request
2025-06-06 12:01:05,348 - app - INFO - Input text length: 52
2025-06-06 12:01:05,348 - app - INFO - Generating XFWB XML...
2025-06-06 12:01:05,348 - xfwb_generator - INFO - Parsing input text: 706-51663054NBOLLW/T1K138.0MC1.01/HUMAN REMAINS
/HUM
2025-06-06 12:01:05,348 - xfwb_generator - INFO - Found AWB: 706-51663054
2025-06-06 12:01:05,349 - xfwb_generator - INFO - Added Handling Instructions codes to AWB 706-51663054: ['HUM']
2025-06-06 12:01:05,349 - xfwb_generator - INFO - Parsed 1 AWBs
2025-06-06 12:01:05,349 - app - INFO - XFWB XML generated successfully, length: 4711
2025-06-06 12:01:20,505 - app - INFO - Received XFWB XML generation request
2025-06-06 12:01:20,505 - app - INFO - Input text length: 61
2025-06-06 12:01:20,505 - app - INFO - Generating XFWB XML...
2025-06-06 12:01:20,505 - xfwb_generator - INFO - Parsing input text: 706-51634332DXBLLW/P3K111.0MC0.26T13/LAPTOPS LITHIUM
/ELI/GEN
2025-06-06 12:01:20,505 - xfwb_generator - WARNING - No valid AWBs found in the input text
2025-06-06 12:01:20,505 - xfwb_generator - WARNING - No AWBs found to generate XML
2025-06-06 12:01:20,505 - app - ERROR - Error in XFWB generation: No valid AWBs found to generate XML
2025-06-06 13:42:50,458 - app - INFO - Serving index.html
2025-06-06 13:43:09,553 - app - INFO - Received Flight Manifest XML generation request
2025-06-06 13:43:09,553 - app - INFO - Input text length: 561
2025-06-06 13:43:09,553 - app - INFO - Generating Flight Manifest XML...
2025-06-06 13:43:09,553 - app - INFO - Processing 24 lines of input
2025-06-06 13:43:09,553 - app - INFO - Processing line: FFM/8
2025-06-06 13:43:09,553 - app - INFO - Processing line: 1/KQ756/06JUN/NBO/5Y-FFE
2025-06-06 13:43:09,553 - app - INFO - Processing line: LLW
2025-06-06 13:43:09,553 - app - INFO - Processing line: 706-51634332DXBLLW/P1K90.0MC0.50/HUMAN REMAINS
2025-06-06 13:43:09,554 - app - INFO - Found AWB line: 706-51634332DXBLLW/P1K90.0MC0.50/HUMAN REMAINS
2025-06-06 13:43:09,554 - app - INFO - Adding AWB: {'prefix': '706', 'number': '51634332', 'origin': 'DXB', 'destination': 'LLW', 'split_code': 'P', 'pieces': '1', 'weight': '90.0', 'volume': '0.50', 'description': 'HUMAN REMAINS', 'special_codes': [], 'uld': None}
2025-06-06 13:43:09,554 - app - INFO - Processing line: /HUM
2025-06-06 13:43:09,554 - app - INFO - Found special code line: /HUM
2025-06-06 13:43:09,554 - app - INFO - Processing line: 706-60497953BOMLLW/P5K100.0MC0.60/COURIER DHL
2025-06-06 13:43:09,554 - app - INFO - Found AWB line: 706-60497953BOMLLW/P5K100.0MC0.60/COURIER DHL
2025-06-06 13:43:09,554 - app - INFO - Adding AWB: {'prefix': '706', 'number': '60497953', 'origin': 'BOM', 'destination': 'LLW', 'split_code': 'P', 'pieces': '5', 'weight': '100.0', 'volume': '0.60', 'description': 'COURIER DHL', 'special_codes': [], 'uld': None}
2025-06-06 13:43:09,554 - app - INFO - Processing line: /COU
2025-06-06 13:43:09,554 - app - INFO - Found special code line: /COU
2025-06-06 13:43:09,554 - app - INFO - Processing line: 706-60606280NBOLLW/P1K5.0MC0.03/DOG
2025-06-06 13:43:09,555 - app - INFO - Found AWB line: 706-60606280NBOLLW/P1K5.0MC0.03/DOG
2025-06-06 13:43:09,555 - app - INFO - Adding AWB: {'prefix': '706', 'number': '60606280', 'origin': 'NBO', 'destination': 'LLW', 'split_code': 'P', 'pieces': '1', 'weight': '5.0', 'volume': '0.03', 'description': 'DOG', 'special_codes': [], 'uld': None}
2025-06-06 13:43:09,555 - app - INFO - Processing line: /AVI
2025-06-06 13:43:09,555 - app - INFO - Found special code line: /AVI
2025-06-06 13:43:09,555 - app - INFO - Processing line: 706-41026672DXBLLW/P1K5.0MC0.03/MEDICAL DRUGS
2025-06-06 13:43:09,555 - app - INFO - Found AWB line: 706-41026672DXBLLW/P1K5.0MC0.03/MEDICAL DRUGS
2025-06-06 13:43:09,556 - app - INFO - Adding AWB: {'prefix': '706', 'number': '41026672', 'origin': 'DXB', 'destination': 'LLW', 'split_code': 'P', 'pieces': '1', 'weight': '5.0', 'volume': '0.03', 'description': 'MEDICAL DRUGS', 'special_codes': [], 'uld': None}
2025-06-06 13:43:09,556 - app - INFO - Processing line: /PHM
2025-06-06 13:43:09,556 - app - INFO - Found special code line: /PHM
2025-06-06 13:43:09,556 - app - INFO - Processing line: 706-00000001DXBLLW/P10K500.0MC2.50/LITHIUM BATTERIES
2025-06-06 13:43:09,556 - app - INFO - Found AWB line: 706-00000001DXBLLW/P10K500.0MC2.50/LITHIUM BATTERIES
2025-06-06 13:43:09,556 - app - INFO - Adding AWB: {'prefix': '706', 'number': '00000001', 'origin': 'DXB', 'destination': 'LLW', 'split_code': 'P', 'pieces': '10', 'weight': '500.0', 'volume': '2.50', 'description': 'LITHIUM BATTERIES', 'special_codes': [], 'uld': None}
2025-06-06 13:43:09,556 - app - INFO - Processing line: /ELI/DGR
2025-06-06 13:43:09,556 - app - INFO - Found special code line: /ELI/DGR
2025-06-06 13:43:09,557 - app - INFO - Processing line: 706-00000002DXBLLW/P1K10.0MC0.06/CLOTHES
2025-06-06 13:43:09,557 - app - INFO - Found AWB line: 706-00000002DXBLLW/P1K10.0MC0.06/CLOTHES
2025-06-06 13:43:09,557 - app - INFO - Adding AWB: {'prefix': '706', 'number': '00000002', 'origin': 'DXB', 'destination': 'LLW', 'split_code': 'P', 'pieces': '1', 'weight': '10.0', 'volume': '0.06', 'description': 'CLOTHES', 'special_codes': [], 'uld': None}
2025-06-06 13:43:09,557 - app - INFO - Processing line: /GEN
2025-06-06 13:43:09,557 - app - INFO - Found special code line: /GEN
2025-06-06 13:43:09,557 - app - INFO - Processing line: 706-00000003DXBLLW/T1K200.0MC1.00/CONSOLIDATED SHIPMENT
2025-06-06 13:43:09,557 - app - INFO - Found AWB line: 706-00000003DXBLLW/T1K200.0MC1.00/CONSOLIDATED SHIPMENT
2025-06-06 13:43:09,557 - app - INFO - Adding AWB: {'prefix': '706', 'number': '00000003', 'origin': 'DXB', 'destination': 'LLW', 'split_code': 'T', 'pieces': '1', 'weight': '200.0', 'volume': '1.00', 'description': 'CONSOLIDATED SHIPMENT', 'special_codes': [], 'uld': None}
2025-06-06 13:43:09,557 - app - INFO - Processing line: /GEN
2025-06-06 13:43:09,557 - app - INFO - Found special code line: /GEN
2025-06-06 13:43:09,558 - app - INFO - Processing line: 706-00000004DXBLLW/T100K234.4MC1.17/MAIL
2025-06-06 13:43:09,558 - app - INFO - Found AWB line: 706-00000004DXBLLW/T100K234.4MC1.17/MAIL
2025-06-06 13:43:09,558 - app - INFO - Adding AWB: {'prefix': '706', 'number': '00000004', 'origin': 'DXB', 'destination': 'LLW', 'split_code': 'T', 'pieces': '100', 'weight': '234.4', 'volume': '1.17', 'description': 'MAIL', 'special_codes': [], 'uld': None}
2025-06-06 13:43:09,558 - app - INFO - Processing line: /MAL
2025-06-06 13:43:09,558 - app - INFO - Found special code line: /MAL
2025-06-06 13:43:09,558 - app - INFO - Processing line: 706-00000005DXBLLW/P50K354.0MC1.77/LAPTOPS
2025-06-06 13:43:09,558 - app - INFO - Found AWB line: 706-00000005DXBLLW/P50K354.0MC1.77/LAPTOPS
2025-06-06 13:43:09,558 - app - INFO - Adding AWB: {'prefix': '706', 'number': '00000005', 'origin': 'DXB', 'destination': 'LLW', 'split_code': 'P', 'pieces': '50', 'weight': '354.0', 'volume': '1.77', 'description': 'LAPTOPS', 'special_codes': [], 'uld': None}
2025-06-06 13:43:09,558 - app - INFO - Processing line: /GEN
2025-06-06 13:43:09,559 - app - INFO - Found special code line: /GEN
2025-06-06 13:43:09,559 - app - INFO - Processing line: 706-00000006DXBLLW/T40K9564.0MC47.82/CONSOLIDATED CARGO
2025-06-06 13:43:09,559 - app - INFO - Found AWB line: 706-00000006DXBLLW/T40K9564.0MC47.82/CONSOLIDATED CARGO
2025-06-06 13:43:09,559 - app - INFO - Adding AWB: {'prefix': '706', 'number': '00000006', 'origin': 'DXB', 'destination': 'LLW', 'split_code': 'T', 'pieces': '40', 'weight': '9564.0', 'volume': '47.82', 'description': 'CONSOLIDATED CARGO', 'special_codes': [], 'uld': None}
2025-06-06 13:43:09,559 - app - INFO - Processing line: /GEN
2025-06-06 13:43:09,559 - app - INFO - Found special code line: /GEN
2025-06-06 13:43:09,559 - app - INFO - Processing line: APL/NIL
2025-06-06 13:43:09,559 - app - INFO - Found 10 AWBs in the input
2025-06-06 13:43:09,559 - app - INFO - Found flight line: 1/KQ756/06JUN/NBO/5Y-FFE
2025-06-06 13:43:09,559 - app - INFO - Found arrival airport: LLW
2025-06-06 13:43:09,560 - app - INFO - Flight details: KQ756, 06JUN, NBO, LLW, 5Y-FFE
2025-06-06 13:43:09,560 - app - INFO - Formatted departure date: 2025-06-06T00:00:00
2025-06-06 13:43:09,560 - app - INFO - Generating XML for ULD: PMC01921KQ
2025-06-06 13:43:09,560 - app - INFO - Adding AWB to ULD 0: 706-51634332
2025-06-06 13:43:09,560 - app - INFO - Adding AWB to ULD 0: 706-60497953
2025-06-06 13:43:09,560 - app - INFO - Adding AWB to ULD 0: 706-60606280
2025-06-06 13:43:09,560 - app - INFO - Adding AWB to ULD 0: 706-41026672
2025-06-06 13:43:09,560 - app - INFO - Adding AWB to ULD 0: 706-00000001
2025-06-06 13:43:09,560 - app - INFO - Adding AWB to ULD 0: 706-00000002
2025-06-06 13:43:09,560 - app - INFO - Adding AWB to ULD 0: 706-00000003
2025-06-06 13:43:09,560 - app - INFO - Adding AWB to ULD 0: 706-00000004
2025-06-06 13:43:09,560 - app - INFO - Adding AWB to ULD 0: 706-00000005
2025-06-06 13:43:09,560 - app - INFO - Adding AWB to ULD 0: 706-00000006
2025-06-06 13:43:09,560 - app - INFO - XML generated successfully, length: 11668
2025-06-06 17:02:41,776 - app - INFO - IATA XML Generator startup
2025-06-06 17:02:41,777 - app - INFO - IATA XML Generator startup
2025-06-06 17:02:41,780 - app - INFO - Static folder path: /var/www/xmlmaker/frontend/static
2025-06-06 17:02:41,780 - app - INFO - IATA XML Generator startup
2025-06-06 17:02:41,781 - app - INFO - Static folder path: /var/www/xmlmaker/frontend/static
2025-06-06 17:02:41,778 - app - INFO - IATA XML Generator startup
2025-06-06 17:02:41,786 - app - INFO - Static folder path: /var/www/xmlmaker/frontend/static
2025-06-06 17:02:41,790 - app - INFO - Static folder path: /var/www/xmlmaker/frontend/static
2025-06-10 08:55:58,195 - app - INFO - IATA XML Generator startup
2025-06-10 08:55:58,197 - app - INFO - IATA XML Generator startup
2025-06-10 08:55:58,197 - app - INFO - Static folder path: /var/www/xmlmaker/frontend/static
2025-06-10 08:55:58,197 - app - INFO - Static folder path: /var/www/xmlmaker/frontend/static
2025-06-10 08:55:58,196 - app - INFO - IATA XML Generator startup
2025-06-10 08:55:58,193 - app - INFO - IATA XML Generator startup
2025-06-10 08:55:58,202 - app - INFO - Static folder path: /var/www/xmlmaker/frontend/static
2025-06-10 08:55:58,206 - app - INFO - Static folder path: /var/www/xmlmaker/frontend/static
2025-06-11 09:02:06,702 - app - INFO - Serving static file: xml-import
2025-06-11 09:02:06,999 - app - INFO - Serving static file: favicon.ico
2025-06-11 09:23:07,359 - app - INFO - Serving index.html
2025-06-11 09:23:08,761 - app - INFO - Serving static file: favicon.ico
2025-06-11 09:23:10,813 - app - INFO - Serving static file: xfwb.html
2025-06-11 11:50:46,415 - app - INFO - Serving index.html
2025-06-11 11:50:47,735 - app - INFO - Serving static file: favicon.ico
2025-06-11 11:50:52,048 - app - INFO - Received Flight Manifest XML generation request
2025-06-11 11:50:52,048 - app - INFO - Input text length: 395
2025-06-11 11:50:52,048 - app - INFO - Generating Flight Manifest XML...
2025-06-11 11:50:52,048 - app - INFO - Processing 15 lines of input
2025-06-11 11:50:52,048 - app - INFO - Processing line: FFM/8
2025-06-11 11:50:52,049 - app - INFO - Processing line: 1/KQ756/11JUN/NBO/5Y-FFH
2025-06-11 11:50:52,049 - app - INFO - Processing line: LLW
2025-06-11 11:50:52,049 - app - INFO - Processing line: 706-51764053SZXLLW/T1K0.58MC0.58/ASSORTED ELECTRONICS
2025-06-11 11:50:52,049 - app - INFO - Found AWB line: 706-51764053SZXLLW/T1K0.58MC0.58/ASSORTED ELECTRONICS
2025-06-11 11:50:52,050 - app - INFO - Adding AWB: {'prefix': '706', 'number': '51764053', 'origin': 'SZX', 'destination': 'LLW', 'split_code': 'T', 'pieces': '1', 'weight': '0.58', 'volume': '0.58', 'description': 'ASSORTED ELECTRONICS', 'special_codes': [], 'uld': None}
2025-06-11 11:50:52,050 - app - INFO - Processing line: /GEN
2025-06-11 11:50:52,050 - app - INFO - Found special code line: /GEN
2025-06-11 11:50:52,050 - app - INFO - Processing line: 706-51701392ZRHLLW/T1K0.23MC0.23/UNCIRCULATED CURRENCY
2025-06-11 11:50:52,050 - app - INFO - Found AWB line: 706-51701392ZRHLLW/T1K0.23MC0.23/UNCIRCULATED CURRENCY
2025-06-11 11:50:52,050 - app - INFO - Adding AWB: {'prefix': '706', 'number': '51701392', 'origin': 'ZRH', 'destination': 'LLW', 'split_code': 'T', 'pieces': '1', 'weight': '0.23', 'volume': '0.23', 'description': 'UNCIRCULATED CURRENCY', 'special_codes': [], 'uld': None}
2025-06-11 11:50:52,050 - app - INFO - Processing line: /VAL
2025-06-11 11:50:52,050 - app - INFO - Found special code line: /VAL
2025-06-11 11:50:52,051 - app - INFO - Processing line: 706-60613420SINLLW/T1K0.045MC0.045/LEGAL DOCS & DEVICES
2025-06-11 11:50:52,051 - app - INFO - Found AWB line: 706-60613420SINLLW/T1K0.045MC0.045/LEGAL DOCS & DEVICES
2025-06-11 11:50:52,051 - app - INFO - Adding AWB: {'prefix': '706', 'number': '60613420', 'origin': 'SIN', 'destination': 'LLW', 'split_code': 'T', 'pieces': '1', 'weight': '0.045', 'volume': '0.045', 'description': 'LEGAL DOCS & DEVICES', 'special_codes': [], 'uld': None}
2025-06-11 11:50:52,051 - app - INFO - Processing line: /COU
2025-06-11 11:50:52,051 - app - INFO - Found special code line: /COU
2025-06-11 11:50:52,051 - app - INFO - Processing line: 706-41026904FRALLW/T1K0.15MC0.15/LITHIUM BATTERIES UN3480
2025-06-11 11:50:52,051 - app - INFO - Found AWB line: 706-41026904FRALLW/T1K0.15MC0.15/LITHIUM BATTERIES UN3480
2025-06-11 11:50:52,052 - app - INFO - Adding AWB: {'prefix': '706', 'number': '41026904', 'origin': 'FRA', 'destination': 'LLW', 'split_code': 'T', 'pieces': '1', 'weight': '0.15', 'volume': '0.15', 'description': 'LITHIUM BATTERIES UN3480', 'special_codes': [], 'uld': None}
2025-06-11 11:50:52,052 - app - INFO - Processing line: /DGR
2025-06-11 11:50:52,052 - app - INFO - Found special code line: /DGR
2025-06-11 11:50:52,052 - app - INFO - Processing line: 706-60612812LHRLLW/T1K0.67MC0.67/CONSOLIDATED CARGO
2025-06-11 11:50:52,052 - app - INFO - Found AWB line: 706-60612812LHRLLW/T1K0.67MC0.67/CONSOLIDATED CARGO
2025-06-11 11:50:52,052 - app - INFO - Adding AWB: {'prefix': '706', 'number': '60612812', 'origin': 'LHR', 'destination': 'LLW', 'split_code': 'T', 'pieces': '1', 'weight': '0.67', 'volume': '0.67', 'description': 'CONSOLIDATED CARGO', 'special_codes': [], 'uld': None}
2025-06-11 11:50:52,052 - app - INFO - Processing line: /CON
2025-06-11 11:50:52,052 - app - INFO - Found special code line: /CON
2025-06-11 11:50:52,052 - app - INFO - Processing line: 706-51689680FRALLW/T1K0.095MC0.095/MED. DIAGNOSTIC KITS
2025-06-11 11:50:52,052 - app - INFO - Found AWB line: 706-51689680FRALLW/T1K0.095MC0.095/MED. DIAGNOSTIC KITS
2025-06-11 11:50:52,053 - app - INFO - Adding AWB: {'prefix': '706', 'number': '51689680', 'origin': 'FRA', 'destination': 'LLW', 'split_code': 'T', 'pieces': '1', 'weight': '0.095', 'volume': '0.095', 'description': 'MED. DIAGNOSTIC KITS', 'special_codes': [], 'uld': None}
2025-06-11 11:50:52,053 - app - INFO - Processing line: /SPX
2025-06-11 11:50:52,053 - app - INFO - Found special code line: /SPX
2025-06-11 11:50:52,053 - app - INFO - Found 6 AWBs in the input
2025-06-11 11:50:52,054 - app - INFO - Found flight line: 1/KQ756/11JUN/NBO/5Y-FFH
2025-06-11 11:50:52,054 - app - INFO - Found arrival airport: LLW
2025-06-11 11:50:52,054 - app - INFO - Flight details: KQ756, 11JUN, NBO, LLW, 5Y-FFH
2025-06-11 11:50:52,055 - app - INFO - Formatted departure date: 2025-06-11T00:00:00
2025-06-11 11:50:52,055 - app - INFO - Generating XML for ULD: PMC01921KQ
2025-06-11 11:50:52,055 - app - INFO - Adding AWB to ULD 0: 706-51764053
2025-06-11 11:50:52,055 - app - INFO - Adding AWB to ULD 0: 706-51701392
2025-06-11 11:50:52,055 - app - INFO - Adding AWB to ULD 0: 706-60613420
2025-06-11 11:50:52,055 - app - INFO - Adding AWB to ULD 0: 706-41026904
2025-06-11 11:50:52,056 - app - INFO - Adding AWB to ULD 0: 706-60612812
2025-06-11 11:50:52,056 - app - INFO - Adding AWB to ULD 0: 706-51689680
2025-06-11 11:50:52,056 - app - INFO - XML generated successfully, length: 8034
2025-06-11 12:01:18,737 - app - INFO - IATA XML Generator startup
2025-06-11 12:01:18,739 - app - INFO - IATA XML Generator startup
2025-06-11 12:01:18,741 - app - INFO - IATA XML Generator startup
2025-06-11 12:01:18,745 - app - INFO - Static folder path: /var/www/xmlmaker/frontend/static
2025-06-11 12:01:18,745 - app - INFO - IATA XML Generator startup
2025-06-11 12:01:18,747 - app - INFO - Static folder path: /var/www/xmlmaker/frontend/static
2025-06-11 12:01:18,744 - app - INFO - Static folder path: /var/www/xmlmaker/frontend/static
2025-06-11 12:01:18,744 - app - INFO - Static folder path: /var/www/xmlmaker/frontend/static
2025-06-11 13:22:16,780 - app - INFO - IATA XML Generator startup
2025-06-11 13:22:16,787 - app - INFO - Static folder path: /var/www/xmlmaker/frontend/static
2025-06-11 13:22:16,782 - app - INFO - IATA XML Generator startup
2025-06-11 13:22:16,788 - app - INFO - Static folder path: /var/www/xmlmaker/frontend/static
2025-06-11 13:22:16,788 - app - INFO - IATA XML Generator startup
2025-06-11 13:22:16,789 - app - INFO - Static folder path: /var/www/xmlmaker/frontend/static
2025-06-11 13:22:16,785 - app - INFO - IATA XML Generator startup
2025-06-11 13:22:16,789 - app - INFO - Static folder path: /var/www/xmlmaker/frontend/static
2025-06-12 06:16:51,364 - app - INFO - Serving index.html
2025-06-12 06:16:54,087 - app - INFO - Serving static file: favicon.ico
2025-06-12 06:17:22,419 - app - INFO - Received Flight Manifest XML generation request
2025-06-12 06:17:22,420 - app - INFO - Input text length: 217
2025-06-12 06:17:22,420 - app - INFO - Generating Flight Manifest XML...
2025-06-12 06:17:22,420 - app - INFO - Processing 9 lines of input
2025-06-12 06:17:22,420 - app - INFO - Processing line: FFM/8
2025-06-12 06:17:22,421 - app - INFO - Processing line: 1/KQ704/12JUN/NBO/5Y-FFE
2025-06-12 06:17:22,421 - app - INFO - Processing line: LLW
2025-06-12 06:17:22,421 - app - INFO - Processing line: 706-33445566NBOCPT/T2K5.0MC0.01/PAPER DOCUMENTS
2025-06-12 06:17:22,421 - app - INFO - Found AWB line: 706-33445566NBOCPT/T2K5.0MC0.01/PAPER DOCUMENTS
2025-06-12 06:17:22,422 - app - INFO - Adding AWB: {'prefix': '706', 'number': '33445566', 'origin': 'NBO', 'destination': 'CPT', 'split_code': 'T', 'pieces': '2', 'weight': '5.0', 'volume': '0.01', 'description': 'PAPER DOCUMENTS', 'special_codes': [], 'uld': None}
2025-06-12 06:17:22,423 - app - INFO - Processing line: /DOC/GEN
2025-06-12 06:17:22,423 - app - INFO - Found special code line: /DOC/GEN
2025-06-12 06:17:22,423 - app - INFO - Processing line: 706-55667788NBODUB/T1K90.0MC0.03/VETERINARY CERTIFICATE
2025-06-12 06:17:22,423 - app - INFO - Found AWB line: 706-55667788NBODUB/T1K90.0MC0.03/VETERINARY CERTIFICATE
2025-06-12 06:17:22,423 - app - INFO - Adding AWB: {'prefix': '706', 'number': '55667788', 'origin': 'NBO', 'destination': 'DUB', 'split_code': 'T', 'pieces': '1', 'weight': '90.0', 'volume': '0.03', 'description': 'VETERINARY CERTIFICATE', 'special_codes': [], 'uld': None}
2025-06-12 06:17:22,423 - app - INFO - Processing line: /DOC/GEN
2025-06-12 06:17:22,424 - app - INFO - Found special code line: /DOC/GEN
2025-06-12 06:17:22,424 - app - INFO - Processing line: 706-11223344NBOJNB/T4K250.0MC0.06/FLAMMABLE LIQUIDS
2025-06-12 06:17:22,424 - app - INFO - Found AWB line: 706-11223344NBOJNB/T4K250.0MC0.06/FLAMMABLE LIQUIDS
2025-06-12 06:17:22,424 - app - INFO - Adding AWB: {'prefix': '706', 'number': '11223344', 'origin': 'NBO', 'destination': 'JNB', 'split_code': 'T', 'pieces': '4', 'weight': '250.0', 'volume': '0.06', 'description': 'FLAMMABLE LIQUIDS', 'special_codes': [], 'uld': None}
2025-06-12 06:17:22,424 - app - INFO - Processing line: /RFL/DGR
2025-06-12 06:17:22,424 - app - INFO - Found special code line: /RFL/DGR
2025-06-12 06:17:22,424 - app - INFO - Found 3 AWBs in the input
2025-06-12 06:17:22,425 - app - INFO - Found flight line: 1/KQ704/12JUN/NBO/5Y-FFE
2025-06-12 06:17:22,425 - app - INFO - Found arrival airport: LLW
2025-06-12 06:17:22,425 - app - INFO - Flight details: KQ704, 12JUN, NBO, LLW, 5Y-FFE
2025-06-12 06:17:22,426 - app - INFO - Formatted departure date: 2025-06-12T00:00:00
2025-06-12 06:17:22,426 - app - INFO - Generating XML for ULD: PMC01921KQ
2025-06-12 06:17:22,426 - app - INFO - Adding AWB to ULD 0: 706-*************-06-12 06:17:22,426 - app - INFO - Adding AWB to ULD 0: 706-*************-06-12 06:17:22,426 - app - INFO - Adding AWB to ULD 0: 706-11223344
2025-06-12 06:17:22,426 - app - INFO - XML generated successfully, length: 5738
2025-06-12 06:18:12,150 - app - INFO - Received Flight Manifest XML generation request
2025-06-12 06:18:12,153 - app - INFO - Input text length: 217
2025-06-12 06:18:12,154 - app - INFO - Generating Flight Manifest XML...
2025-06-12 06:18:12,154 - app - INFO - Processing 9 lines of input
2025-06-12 06:18:12,154 - app - INFO - Processing line: FFM/8
2025-06-12 06:18:12,154 - app - INFO - Processing line: 1/KQ704/12JUN/JNB/5Y-FFE
2025-06-12 06:18:12,154 - app - INFO - Processing line: LLW
2025-06-12 06:18:12,155 - app - INFO - Processing line: 706-33445566CPTLLW/T2K5.0MC0.01/PAPER DOCUMENTS
2025-06-12 06:18:12,155 - app - INFO - Found AWB line: 706-33445566CPTLLW/T2K5.0MC0.01/PAPER DOCUMENTS
2025-06-12 06:18:12,155 - app - INFO - Adding AWB: {'prefix': '706', 'number': '33445566', 'origin': 'CPT', 'destination': 'LLW', 'split_code': 'T', 'pieces': '2', 'weight': '5.0', 'volume': '0.01', 'description': 'PAPER DOCUMENTS', 'special_codes': [], 'uld': None}
2025-06-12 06:18:12,155 - app - INFO - Processing line: /DOC/GEN
2025-06-12 06:18:12,156 - app - INFO - Found special code line: /DOC/GEN
2025-06-12 06:18:12,156 - app - INFO - Processing line: 706-55667788DUBLLW/T1K90.0MC0.03/VETERINARY CERTIFICATE
2025-06-12 06:18:12,156 - app - INFO - Found AWB line: 706-55667788DUBLLW/T1K90.0MC0.03/VETERINARY CERTIFICATE
2025-06-12 06:18:12,156 - app - INFO - Adding AWB: {'prefix': '706', 'number': '55667788', 'origin': 'DUB', 'destination': 'LLW', 'split_code': 'T', 'pieces': '1', 'weight': '90.0', 'volume': '0.03', 'description': 'VETERINARY CERTIFICATE', 'special_codes': [], 'uld': None}
2025-06-12 06:18:12,156 - app - INFO - Processing line: /DOC/GEN
2025-06-12 06:18:12,156 - app - INFO - Found special code line: /DOC/GEN
2025-06-12 06:18:12,156 - app - INFO - Processing line: 706-11223344JNBLLW/T4K250.0MC0.06/FLAMMABLE LIQUIDS
2025-06-12 06:18:12,157 - app - INFO - Found AWB line: 706-11223344JNBLLW/T4K250.0MC0.06/FLAMMABLE LIQUIDS
2025-06-12 06:18:12,157 - app - INFO - Adding AWB: {'prefix': '706', 'number': '11223344', 'origin': 'JNB', 'destination': 'LLW', 'split_code': 'T', 'pieces': '4', 'weight': '250.0', 'volume': '0.06', 'description': 'FLAMMABLE LIQUIDS', 'special_codes': [], 'uld': None}
2025-06-12 06:18:12,157 - app - INFO - Processing line: /RFL/DGR
2025-06-12 06:18:12,157 - app - INFO - Found special code line: /RFL/DGR
2025-06-12 06:18:12,157 - app - INFO - Found 3 AWBs in the input
2025-06-12 06:18:12,158 - app - INFO - Found flight line: 1/KQ704/12JUN/JNB/5Y-FFE
2025-06-12 06:18:12,158 - app - INFO - Found arrival airport: LLW
2025-06-12 06:18:12,158 - app - INFO - Flight details: KQ704, 12JUN, JNB, LLW, 5Y-FFE
2025-06-12 06:18:12,158 - app - INFO - Formatted departure date: 2025-06-12T00:00:00
2025-06-12 06:18:12,159 - app - INFO - Generating XML for ULD: PMC01921KQ
2025-06-12 06:18:12,159 - app - INFO - Adding AWB to ULD 0: 706-*************-06-12 06:18:12,159 - app - INFO - Adding AWB to ULD 0: 706-*************-06-12 06:18:12,160 - app - INFO - Adding AWB to ULD 0: 706-11223344
2025-06-12 06:18:12,160 - app - INFO - XML generated successfully, length: 5738
2025-06-12 06:20:09,275 - app - INFO - Received Flight Manifest XML generation request
2025-06-12 06:20:09,275 - app - INFO - Input text length: 261
2025-06-12 06:20:09,276 - app - INFO - Generating Flight Manifest XML...
2025-06-12 06:20:09,276 - app - INFO - Processing 11 lines of input
2025-06-12 06:20:09,276 - app - INFO - Processing line: FFM/8
2025-06-12 06:20:09,276 - app - INFO - Processing line: 1/KQ703/12JUN/MEX/5Y-FFE
2025-06-12 06:20:09,276 - app - INFO - Processing line: LLW
2025-06-12 06:20:09,276 - app - INFO - Processing line: 706-77665544DOHLLW/T3K22.0MC0.03/CHEMICAL SAMPLES
2025-06-12 06:20:09,277 - app - INFO - Found AWB line: 706-77665544DOHLLW/T3K22.0MC0.03/CHEMICAL SAMPLES
2025-06-12 06:20:09,277 - app - INFO - Adding AWB: {'prefix': '706', 'number': '77665544', 'origin': 'DOH', 'destination': 'LLW', 'split_code': 'T', 'pieces': '3', 'weight': '22.0', 'volume': '0.03', 'description': 'CHEMICAL SAMPLES', 'special_codes': [], 'uld': None}
2025-06-12 06:20:09,277 - app - INFO - Processing line: /DGR /HEA
2025-06-12 06:20:09,278 - app - INFO - Found special code line: /DGR /HEA
2025-06-12 06:20:09,278 - app - INFO - Processing line: 706-66554433ISTLLW/T2K150.0MC0.04/STEEL BEAMS
2025-06-12 06:20:09,278 - app - INFO - Found AWB line: 706-66554433ISTLLW/T2K150.0MC0.04/STEEL BEAMS
2025-06-12 06:20:09,278 - app - INFO - Adding AWB: {'prefix': '706', 'number': '66554433', 'origin': 'IST', 'destination': 'LLW', 'split_code': 'T', 'pieces': '2', 'weight': '150.0', 'volume': '0.04', 'description': 'STEEL BEAMS', 'special_codes': [], 'uld': None}
2025-06-12 06:20:09,278 - app - INFO - Processing line: /HEA/BIG
2025-06-12 06:20:09,278 - app - INFO - Found special code line: /HEA/BIG
2025-06-12 06:20:09,278 - app - INFO - Processing line: 706-44556677MEXLLW/T1K8.0MC0.01/DRY ICE SHIPMENT
2025-06-12 06:20:09,279 - app - INFO - Found AWB line: 706-44556677MEXLLW/T1K8.0MC0.01/DRY ICE SHIPMENT
2025-06-12 06:20:09,279 - app - INFO - Adding AWB: {'prefix': '706', 'number': '44556677', 'origin': 'MEX', 'destination': 'LLW', 'split_code': 'T', 'pieces': '1', 'weight': '8.0', 'volume': '0.01', 'description': 'DRY ICE SHIPMENT', 'special_codes': [], 'uld': None}
2025-06-12 06:20:09,279 - app - INFO - Processing line: /ICE/RCL
2025-06-12 06:20:09,279 - app - INFO - Found special code line: /ICE/RCL
2025-06-12 06:20:09,279 - app - INFO - Processing line: 706-22334455GRULLW/T3K75.0MC0.05/DANGEROUS GOODS
2025-06-12 06:20:09,279 - app - INFO - Found AWB line: 706-22334455GRULLW/T3K75.0MC0.05/DANGEROUS GOODS
2025-06-12 06:20:09,280 - app - INFO - Adding AWB: {'prefix': '706', 'number': '22334455', 'origin': 'GRU', 'destination': 'LLW', 'split_code': 'T', 'pieces': '3', 'weight': '75.0', 'volume': '0.05', 'description': 'DANGEROUS GOODS', 'special_codes': [], 'uld': None}
2025-06-12 06:20:09,280 - app - INFO - Processing line: /DGR
2025-06-12 06:20:09,280 - app - INFO - Found special code line: /DGR
2025-06-12 06:20:09,280 - app - INFO - Found 4 AWBs in the input
2025-06-12 06:20:09,280 - app - INFO - Found flight line: 1/KQ703/12JUN/MEX/5Y-FFE
2025-06-12 06:20:09,281 - app - INFO - Found arrival airport: LLW
2025-06-12 06:20:09,281 - app - INFO - Flight details: KQ703, 12JUN, MEX, LLW, 5Y-FFE
2025-06-12 06:20:09,281 - app - INFO - Formatted departure date: 2025-06-12T00:00:00
2025-06-12 06:20:09,281 - app - INFO - Generating XML for ULD: PMC01921KQ
2025-06-12 06:20:09,281 - app - INFO - Adding AWB to ULD 0: 706-77665544
2025-06-12 06:20:09,282 - app - INFO - Adding AWB to ULD 0: 706-66554433
2025-06-12 06:20:09,282 - app - INFO - Adding AWB to ULD 0: 706-44556677
2025-06-12 06:20:09,282 - app - INFO - Adding AWB to ULD 0: 706-22334455
2025-06-12 06:20:09,282 - app - INFO - XML generated successfully, length: 6617
2025-06-12 06:21:47,998 - app - INFO - Received Flight Manifest XML generation request
2025-06-12 06:21:47,999 - app - INFO - Input text length: 452
2025-06-12 06:21:47,999 - app - INFO - Generating Flight Manifest XML...
2025-06-12 06:21:48,000 - app - INFO - Processing 17 lines of input
2025-06-12 06:21:48,000 - app - INFO - Processing line: FFM/8
2025-06-12 06:21:48,000 - app - INFO - Processing line: 1/KQ702/12JUN/NBO/5Y-FFE
2025-06-12 06:21:48,000 - app - INFO - Processing line: LLW
2025-06-12 06:21:48,000 - app - INFO - Processing line: 706-77889900DXBLLW/T1K60.0MC0.06/LUXURY WATCHES
2025-06-12 06:21:48,000 - app - INFO - Found AWB line: 706-77889900DXBLLW/T1K60.0MC0.06/LUXURY WATCHES
2025-06-12 06:21:48,001 - app - INFO - Adding AWB: {'prefix': '706', 'number': '77889900', 'origin': 'DXB', 'destination': 'LLW', 'split_code': 'T', 'pieces': '1', 'weight': '60.0', 'volume': '0.06', 'description': 'LUXURY WATCHES', 'special_codes': [], 'uld': None}
2025-06-12 06:21:48,001 - app - INFO - Processing line: /VAL /VUN
2025-06-12 06:21:48,001 - app - INFO - Found special code line: /VAL /VUN
2025-06-12 06:21:48,002 - app - INFO - Processing line: 706-33445566LHRLLW/T3K20.0MC0.02/HUMAN REMAINS
2025-06-12 06:21:48,002 - app - INFO - Found AWB line: 706-33445566LHRLLW/T3K20.0MC0.02/HUMAN REMAINS
2025-06-12 06:21:48,002 - app - INFO - Adding AWB: {'prefix': '706', 'number': '33445566', 'origin': 'LHR', 'destination': 'LLW', 'split_code': 'T', 'pieces': '3', 'weight': '20.0', 'volume': '0.02', 'description': 'HUMAN REMAINS', 'special_codes': [], 'uld': None}
2025-06-12 06:21:48,002 - app - INFO - Processing line: /HUM
2025-06-12 06:21:48,002 - app - INFO - Found special code line: /HUM
2025-06-12 06:21:48,003 - app - INFO - Processing line: 706-66778899SINLLW/T2K45.0MC0.03/FROZEN SEAFOOD
2025-06-12 06:21:48,003 - app - INFO - Found AWB line: 706-66778899SINLLW/T2K45.0MC0.03/FROZEN SEAFOOD
2025-06-12 06:21:48,003 - app - INFO - Adding AWB: {'prefix': '706', 'number': '66778899', 'origin': 'SIN', 'destination': 'LLW', 'split_code': 'T', 'pieces': '2', 'weight': '45.0', 'volume': '0.03', 'description': 'FROZEN SEAFOOD', 'special_codes': [], 'uld': None}
2025-06-12 06:21:48,003 - app - INFO - Processing line: /FRO /PER
2025-06-12 06:21:48,003 - app - INFO - Found special code line: /FRO /PER
2025-06-12 06:21:48,003 - app - INFO - Processing line: 706-00112233ICNLLW/T1K55.0MC0.05/LIVE TROPICAL FISH
2025-06-12 06:21:48,003 - app - INFO - Found AWB line: 706-00112233ICNLLW/T1K55.0MC0.05/LIVE TROPICAL FISH
2025-06-12 06:21:48,003 - app - INFO - Adding AWB: {'prefix': '706', 'number': '00112233', 'origin': 'ICN', 'destination': 'LLW', 'split_code': 'T', 'pieces': '1', 'weight': '55.0', 'volume': '0.05', 'description': 'LIVE TROPICAL FISH', 'special_codes': [], 'uld': None}
2025-06-12 06:21:48,003 - app - INFO - Processing line: /AVI /LIV
2025-06-12 06:21:48,003 - app - INFO - Found special code line: /AVI /LIV
2025-06-12 06:21:48,004 - app - INFO - Processing line: 706-88997766SYDLLW/T4K180.0MC0.07/CONSTRUCTION MACHINERY
2025-06-12 06:21:48,004 - app - INFO - Found AWB line: 706-88997766SYDLLW/T4K180.0MC0.07/CONSTRUCTION MACHINERY
2025-06-12 06:21:48,004 - app - INFO - Adding AWB: {'prefix': '706', 'number': '88997766', 'origin': 'SYD', 'destination': 'LLW', 'split_code': 'T', 'pieces': '4', 'weight': '180.0', 'volume': '0.07', 'description': 'CONSTRUCTION MACHINERY', 'special_codes': [], 'uld': None}
2025-06-12 06:21:48,004 - app - INFO - Processing line: /BIG/HEA
2025-06-12 06:21:48,004 - app - INFO - Found special code line: /BIG/HEA
2025-06-12 06:21:48,004 - app - INFO - Processing line: 706-55443322DELLLW/T2K10.0MC0.01/DIPLOMATIC MAIL
2025-06-12 06:21:48,004 - app - INFO - Found AWB line: 706-55443322DELLLW/T2K10.0MC0.01/DIPLOMATIC MAIL
2025-06-12 06:21:48,004 - app - INFO - Adding AWB: {'prefix': '706', 'number': '55443322', 'origin': 'DEL', 'destination': 'LLW', 'split_code': 'T', 'pieces': '2', 'weight': '10.0', 'volume': '0.01', 'description': 'DIPLOMATIC MAIL', 'special_codes': [], 'uld': None}
2025-06-12 06:21:48,004 - app - INFO - Processing line: /DIP/DOC
2025-06-12 06:21:48,004 - app - INFO - Found special code line: /DIP/DOC
2025-06-12 06:21:48,004 - app - INFO - Processing line: 706-99887766HKGLLW/T1K12.0MC0.02/LITHIUM BATTERIES
2025-06-12 06:21:48,004 - app - INFO - Found AWB line: 706-99887766HKGLLW/T1K12.0MC0.02/LITHIUM BATTERIES
2025-06-12 06:21:48,004 - app - INFO - Adding AWB: {'prefix': '706', 'number': '99887766', 'origin': 'HKG', 'destination': 'LLW', 'split_code': 'T', 'pieces': '1', 'weight': '12.0', 'volume': '0.02', 'description': 'LITHIUM BATTERIES', 'special_codes': [], 'uld': None}
2025-06-12 06:21:48,005 - app - INFO - Processing line: /ELI/RCL/DGR
2025-06-12 06:21:48,005 - app - INFO - Found special code line: /ELI/RCL/DGR
2025-06-12 06:21:48,005 - app - INFO - Found 7 AWBs in the input
2025-06-12 06:21:48,005 - app - INFO - Found flight line: 1/KQ702/12JUN/NBO/5Y-FFE
2025-06-12 06:21:48,005 - app - INFO - Found arrival airport: LLW
2025-06-12 06:21:48,005 - app - INFO - Flight details: KQ702, 12JUN, NBO, LLW, 5Y-FFE
2025-06-12 06:21:48,005 - app - INFO - Formatted departure date: 2025-06-12T00:00:00
2025-06-12 06:21:48,005 - app - INFO - Generating XML for ULD: PMC01921KQ
2025-06-12 06:21:48,005 - app - INFO - Adding AWB to ULD 0: 706-77889900
2025-06-12 06:21:48,006 - app - INFO - Adding AWB to ULD 0: 706-*************-06-12 06:21:48,006 - app - INFO - Adding AWB to ULD 0: 706-66778899
2025-06-12 06:21:48,006 - app - INFO - Adding AWB to ULD 0: 706-00112233
2025-06-12 06:21:48,006 - app - INFO - Adding AWB to ULD 0: 706-88997766
2025-06-12 06:21:48,006 - app - INFO - Adding AWB to ULD 0: 706-55443322
2025-06-12 06:21:48,006 - app - INFO - Adding AWB to ULD 0: 706-99887766
2025-06-12 06:21:48,006 - app - INFO - XML generated successfully, length: 9835
2025-06-12 06:23:14,138 - app - INFO - Received Flight Manifest XML generation request
2025-06-12 06:23:14,139 - app - INFO - Input text length: 396
2025-06-12 06:23:14,139 - app - INFO - Generating Flight Manifest XML...
2025-06-12 06:23:14,140 - app - INFO - Processing 15 lines of input
2025-06-12 06:23:14,140 - app - INFO - Processing line: FFM/8
2025-06-12 06:23:14,140 - app - INFO - Processing line: 1/KQ701/12JUN/DAR/5Y-FFE
2025-06-12 06:23:14,140 - app - INFO - Processing line: LLW
2025-06-12 06:23:14,140 - app - INFO - Processing line: 706-60613556NBOLLW/T3K15.0MC0.01/PHARMACEUTICALS
2025-06-12 06:23:14,140 - app - INFO - Found AWB line: 706-60613556NBOLLW/T3K15.0MC0.01/PHARMACEUTICALS
2025-06-12 06:23:14,140 - app - INFO - Adding AWB: {'prefix': '706', 'number': '60613556', 'origin': 'NBO', 'destination': 'LLW', 'split_code': 'T', 'pieces': '3', 'weight': '15.0', 'volume': '0.01', 'description': 'PHARMACEUTICALS', 'special_codes': [], 'uld': None}
2025-06-12 06:23:14,141 - app - INFO - Processing line: /PIL/HEA
2025-06-12 06:23:14,141 - app - INFO - Found special code line: /PIL/HEA
2025-06-12 06:23:14,141 - app - INFO - Processing line: 706-12345675LAXLLW/T1K50.0MC0.05/AIRCRAFT ENGINE PARTS
2025-06-12 06:23:14,141 - app - INFO - Found AWB line: 706-12345675LAXLLW/T1K50.0MC0.05/AIRCRAFT ENGINE PARTS
2025-06-12 06:23:14,141 - app - INFO - Adding AWB: {'prefix': '706', 'number': '12345675', 'origin': 'LAX', 'destination': 'LLW', 'split_code': 'T', 'pieces': '1', 'weight': '50.0', 'volume': '0.05', 'description': 'AIRCRAFT ENGINE PARTS', 'special_codes': [], 'uld': None}
2025-06-12 06:23:14,141 - app - INFO - Processing line: /AXA/HEA
2025-06-12 06:23:14,141 - app - INFO - Found special code line: /AXA/HEA
2025-06-12 06:23:14,141 - app - INFO - Processing line: 706-98765432JFKLLW/T2K30.0MC0.02/ELECTRONICS
2025-06-12 06:23:14,142 - app - INFO - Found AWB line: 706-98765432JFKLLW/T2K30.0MC0.02/ELECTRONICS
2025-06-12 06:23:14,142 - app - INFO - Adding AWB: {'prefix': '706', 'number': '98765432', 'origin': 'JFK', 'destination': 'LLW', 'split_code': 'T', 'pieces': '2', 'weight': '30.0', 'volume': '0.02', 'description': 'ELECTRONICS', 'special_codes': [], 'uld': None}
2025-06-12 06:23:14,142 - app - INFO - Processing line: /MAG /ELM
2025-06-12 06:23:14,142 - app - INFO - Found special code line: /MAG /ELM
2025-06-12 06:23:14,142 - app - INFO - Processing line: 706-11223344AMSLLW/T1K25.0MC0.03/FRESH CUT FLOWERS
2025-06-12 06:23:14,142 - app - INFO - Found AWB line: 706-11223344AMSLLW/T1K25.0MC0.03/FRESH CUT FLOWERS
2025-06-12 06:23:14,142 - app - INFO - Adding AWB: {'prefix': '706', 'number': '11223344', 'origin': 'AMS', 'destination': 'LLW', 'split_code': 'T', 'pieces': '1', 'weight': '25.0', 'volume': '0.03', 'description': 'FRESH CUT FLOWERS', 'special_codes': [], 'uld': None}
2025-06-12 06:23:14,142 - app - INFO - Processing line: /PER/COL
2025-06-12 06:23:14,143 - app - INFO - Found special code line: /PER/COL
2025-06-12 06:23:14,143 - app - INFO - Processing line: 706-55667788CDGLLW/T3K400.0MC0.04/INDUSTRIAL TURBINE
2025-06-12 06:23:14,143 - app - INFO - Found AWB line: 706-55667788CDGLLW/T3K400.0MC0.04/INDUSTRIAL TURBINE
2025-06-12 06:23:14,143 - app - INFO - Adding AWB: {'prefix': '706', 'number': '55667788', 'origin': 'CDG', 'destination': 'LLW', 'split_code': 'T', 'pieces': '3', 'weight': '400.0', 'volume': '0.04', 'description': 'INDUSTRIAL TURBINE', 'special_codes': [], 'uld': None}
2025-06-12 06:23:14,143 - app - INFO - Processing line: /HEA/BIG
2025-06-12 06:23:14,143 - app - INFO - Found special code line: /HEA/BIG
2025-06-12 06:23:14,143 - app - INFO - Processing line: 706-44332211FRALLW/T2K35.0MC0.01/COVID19 VACCINES
2025-06-12 06:23:14,143 - app - INFO - Found AWB line: 706-44332211FRALLW/T2K35.0MC0.01/COVID19 VACCINES
2025-06-12 06:23:14,144 - app - INFO - Adding AWB: {'prefix': '706', 'number': '44332211', 'origin': 'FRA', 'destination': 'LLW', 'split_code': 'T', 'pieces': '2', 'weight': '35.0', 'volume': '0.01', 'description': 'COVID19 VACCINES', 'special_codes': [], 'uld': None}
2025-06-12 06:23:14,144 - app - INFO - Processing line: /PIL/COL/HEA
2025-06-12 06:23:14,144 - app - INFO - Found special code line: /PIL/COL/HEA
2025-06-12 06:23:14,144 - app - INFO - Found 6 AWBs in the input
2025-06-12 06:23:14,144 - app - INFO - Found flight line: 1/KQ701/12JUN/DAR/5Y-FFE
2025-06-12 06:23:14,144 - app - INFO - Found arrival airport: LLW
2025-06-12 06:23:14,144 - app - INFO - Flight details: KQ701, 12JUN, DAR, LLW, 5Y-FFE
2025-06-12 06:23:14,144 - app - INFO - Formatted departure date: 2025-06-12T00:00:00
2025-06-12 06:23:14,145 - app - INFO - Generating XML for ULD: PMC01921KQ
2025-06-12 06:23:14,145 - app - INFO - Adding AWB to ULD 0: 706-*************-06-12 06:23:14,145 - app - INFO - Adding AWB to ULD 0: 706-12345675
2025-06-12 06:23:14,145 - app - INFO - Adding AWB to ULD 0: 706-*************-06-12 06:23:14,145 - app - INFO - Adding AWB to ULD 0: 706-11223344
2025-06-12 06:23:14,145 - app - INFO - Adding AWB to ULD 0: 706-*************-06-12 06:23:14,145 - app - INFO - Adding AWB to ULD 0: 706-44332211
2025-06-12 06:23:14,146 - app - INFO - XML generated successfully, length: 8945
2025-06-12 10:23:35,291 - app - INFO - Serving static file: favicon.ico
2025-06-12 10:35:03,208 - app - INFO - Serving index.html
2025-06-12 10:35:06,368 - app - INFO - Serving static file: xfwb.html
2025-06-12 10:35:14,225 - app - INFO - XFWB sample data requested
2025-06-12 10:35:14,226 - app - INFO - XFWB sample data sent
2025-06-12 10:35:23,072 - app - INFO - Received XFWB XML generation request
2025-06-12 10:35:23,072 - app - INFO - Input text length: 102
2025-06-12 10:35:23,072 - app - INFO - Generating XFWB XML...
2025-06-12 10:35:23,072 - xfwb_generator - INFO - Parsing input text: 706-51663054NBOLLW/T1K138.0MC1.01/HUMAN REMAINS
/HUM
706-60609942NBOLLW/T4K25.0MC0.01/DHL EXPRESS
/COU
2025-06-12 10:35:23,073 - xfwb_generator - INFO - Found AWB: 706-51663054
2025-06-12 10:35:23,073 - xfwb_generator - INFO - Added Handling Instructions codes to AWB 706-51663054: ['HUM']
2025-06-12 10:35:23,073 - xfwb_generator - INFO - Found AWB: 706-60609942
2025-06-12 10:35:23,073 - xfwb_generator - INFO - Added Handling Instructions codes to AWB 706-60609942: ['COU']
2025-06-12 10:35:23,073 - xfwb_generator - INFO - Parsed 2 AWBs
2025-06-12 10:35:23,073 - xfwb_generator - INFO - Generating consolidated XML for 2 AWBs
2025-06-12 10:35:23,073 - xfwb_generator - INFO - Processing AWB 1/2: 706-51663054
2025-06-12 10:35:23,074 - xfwb_generator - INFO - Processing AWB 2/2: 706-60609942
2025-06-12 10:35:23,074 - app - INFO - XFWB XML generated successfully, length: 9452
2025-06-12 10:35:23,074 - app - INFO - Multiple AWBs detected in the response
2025-06-12 10:35:23,074 - app - INFO - Found 2 AWBs in the response
2025-06-12 10:36:57,890 - app - INFO - Serving static file: index.html
2025-06-12 10:37:02,034 - app - INFO - Serving static file: xfwb.html
2025-06-12 10:37:05,162 - app - INFO - XFWB sample data requested
2025-06-12 10:37:05,162 - app - INFO - XFWB sample data sent
2025-06-12 10:43:01,190 - app - INFO - Serving static file: xfwb.html
2025-06-12 10:43:03,123 - app - INFO - Serving static file: favicon.ico
2025-06-12 10:43:10,399 - app - INFO - XFWB sample data requested
2025-06-12 10:43:10,399 - app - INFO - XFWB sample data sent
2025-06-12 10:50:55,004 - app - INFO - Serving static file: xfwb.html
2025-06-12 10:51:02,756 - app - INFO - Received XFWB XML generation request
2025-06-12 10:51:02,756 - app - INFO - Input text length: 1184
2025-06-12 10:51:02,757 - app - INFO - Generating XFWB XML...
2025-06-12 10:51:02,757 - xfwb_generator - INFO - Parsing input text: 706-60613556NBOLLW/T3K15.0MC0.01/PHARMACEUTICALS
/PIL/HEA
706-12345675LAXLLW/T1K50.0MC0.05/AIRCRAFT ENGINE PARTS
/AXA/HEA
706-98765432JFKLLW/T2K30.0MC0.02/ELECTRONICS
/MAG/ELM
706-11223344AMSLLW/T1K25.0MC0.03/FRESH CUT FLOWERS
/PER/COL
706-55667788CDGLLW/T3K400.0MC0.04/INDUSTRIAL TURBINE
/HEA/BIG
706-44332211FRALLW/T2K35.0MC0.01/COVID19 VACCINES
/PIL/COL/HEA
706-77889900DXBLLW/T1K60.0MC0.06/LUXURY WATCHES
/VAL/VUN
706-33445566LHRLLW/T3K20.0MC0.02/HUMAN REMAINS
/HUM
706-66778899SINLLW/T2K45.0MC0.03/FROZEN SEAFOOD
/FRO/PER
706-00112233ICNLLW/T1K55.0MC0.05/LIVE TROPICAL FISH
/AVI/LIV
706-88997766SYDLLW/T4K180.0MC0.07/CONSTRUCTION MACHINERY
/BIG/HEA
706-55443322DELLLW/T2K10.0MC0.01/DIPLOMATIC MAIL
/DIP/DOC
706-99887766HKGLLW/T1K12.0MC0.02/LITHIUM BATTERIES
/ELI/RCL/DGR
706-77665544DOHLLW/T3K22.0MC0.03/CHEMICAL SAMPLES
/DGR/HEA
706-66554433ISTLLW/T2K150.0MC0.04/STEEL BEAMS
/HEA/BIG
706-44556677MEXLLW/T1K8.0MC0.01/DRY ICE SHIPMENT
/ICE/RCL
706-22334455GRULLW/T3K75.0MC0.05/DANGEROUS GOODS
/DGR
706-33445566CPTLLW/T2K5.0MC0.01/PAPER DOCUMENTS
/DOC/GEN
706-55667788DUBLLW/T1K90.0MC0.03/VETERINARY CERTIFICATE
/DOC/GEN
706-11223344JNBLLW/T4K250.0MC0.06/FLAMMABLE LIQUIDS
/RFL/DGR
2025-06-12 10:51:02,757 - xfwb_generator - INFO - Found AWB: 706-*************-06-12 10:51:02,757 - xfwb_generator - INFO - Added Handling Instructions codes to AWB 706-60613556: ['PIL', 'HEA']
2025-06-12 10:51:02,757 - xfwb_generator - INFO - Found AWB: 706-12345675
2025-06-12 10:51:02,757 - xfwb_generator - INFO - Added Handling Instructions codes to AWB 706-12345675: ['AXA', 'HEA']
2025-06-12 10:51:02,758 - xfwb_generator - INFO - Found AWB: 706-*************-06-12 10:51:02,758 - xfwb_generator - INFO - Added Handling Instructions codes to AWB 706-98765432: ['MAG', 'ELM']
2025-06-12 10:51:02,758 - xfwb_generator - INFO - Found AWB: 706-11223344
2025-06-12 10:51:02,758 - xfwb_generator - INFO - Added Handling Instructions codes to AWB 706-11223344: ['PER', 'COL']
2025-06-12 10:51:02,758 - xfwb_generator - INFO - Found AWB: 706-*************-06-12 10:51:02,758 - xfwb_generator - INFO - Added Handling Instructions codes to AWB 706-55667788: ['HEA', 'BIG']
2025-06-12 10:51:02,758 - xfwb_generator - INFO - Found AWB: 706-44332211
2025-06-12 10:51:02,758 - xfwb_generator - INFO - Added Handling Instructions codes to AWB 706-44332211: ['PIL', 'COL', 'HEA']
2025-06-12 10:51:02,758 - xfwb_generator - INFO - Found AWB: 706-77889900
2025-06-12 10:51:02,758 - xfwb_generator - INFO - Added Handling Instructions codes to AWB 706-77889900: ['VAL', 'VUN']
2025-06-12 10:51:02,759 - xfwb_generator - INFO - Found AWB: 706-*************-06-12 10:51:02,759 - xfwb_generator - INFO - Added Handling Instructions codes to AWB 706-33445566: ['HUM']
2025-06-12 10:51:02,759 - xfwb_generator - INFO - Found AWB: 706-66778899
2025-06-12 10:51:02,759 - xfwb_generator - INFO - Added Handling Instructions codes to AWB 706-66778899: ['FRO', 'PER']
2025-06-12 10:51:02,759 - xfwb_generator - INFO - Found AWB: 706-00112233
2025-06-12 10:51:02,759 - xfwb_generator - INFO - Added Handling Instructions codes to AWB 706-00112233: ['AVI', 'LIV']
2025-06-12 10:51:02,759 - xfwb_generator - INFO - Found AWB: 706-88997766
2025-06-12 10:51:02,759 - xfwb_generator - INFO - Added Handling Instructions codes to AWB 706-88997766: ['BIG', 'HEA']
2025-06-12 10:51:02,759 - xfwb_generator - INFO - Found AWB: 706-55443322
2025-06-12 10:51:02,759 - xfwb_generator - INFO - Added Handling Instructions codes to AWB 706-55443322: ['DIP', 'DOC']
2025-06-12 10:51:02,759 - xfwb_generator - INFO - Found AWB: 706-99887766
2025-06-12 10:51:02,759 - xfwb_generator - INFO - Added Handling Instructions codes to AWB 706-99887766: ['ELI', 'RCL', 'DGR']
2025-06-12 10:51:02,759 - xfwb_generator - INFO - Found AWB: 706-77665544
2025-06-12 10:51:02,760 - xfwb_generator - INFO - Added Handling Instructions codes to AWB 706-77665544: ['DGR', 'HEA']
2025-06-12 10:51:02,760 - xfwb_generator - INFO - Found AWB: 706-66554433
2025-06-12 10:51:02,760 - xfwb_generator - INFO - Added Handling Instructions codes to AWB 706-66554433: ['HEA', 'BIG']
2025-06-12 10:51:02,760 - xfwb_generator - INFO - Found AWB: 706-44556677
2025-06-12 10:51:02,760 - xfwb_generator - INFO - Added Handling Instructions codes to AWB 706-44556677: ['ICE', 'RCL']
2025-06-12 10:51:02,760 - xfwb_generator - INFO - Found AWB: 706-22334455
2025-06-12 10:51:02,760 - xfwb_generator - INFO - Added Handling Instructions codes to AWB 706-22334455: ['DGR']
2025-06-12 10:51:02,760 - xfwb_generator - INFO - Found AWB: 706-*************-06-12 10:51:02,761 - xfwb_generator - INFO - Added Handling Instructions codes to AWB 706-33445566: ['DOC', 'GEN']
2025-06-12 10:51:02,761 - xfwb_generator - INFO - Found AWB: 706-*************-06-12 10:51:02,761 - xfwb_generator - INFO - Added Handling Instructions codes to AWB 706-55667788: ['DOC', 'GEN']
2025-06-12 10:51:02,761 - xfwb_generator - INFO - Found AWB: 706-11223344
2025-06-12 10:51:02,761 - xfwb_generator - INFO - Added Handling Instructions codes to AWB 706-11223344: ['RFL', 'DGR']
2025-06-12 10:51:02,761 - xfwb_generator - INFO - Parsed 20 AWBs
2025-06-12 10:51:02,761 - xfwb_generator - INFO - Generating consolidated XML for 20 AWBs
2025-06-12 10:51:02,761 - xfwb_generator - INFO - Processing AWB 1/20: 706-*************-06-12 10:51:02,762 - xfwb_generator - INFO - Processing AWB 2/20: 706-12345675
2025-06-12 10:51:02,763 - xfwb_generator - INFO - Processing AWB 3/20: 706-*************-06-12 10:51:02,764 - xfwb_generator - INFO - Processing AWB 4/20: 706-11223344
2025-06-12 10:51:02,764 - xfwb_generator - INFO - Processing AWB 5/20: 706-*************-06-12 10:51:02,765 - xfwb_generator - INFO - Processing AWB 6/20: 706-44332211
2025-06-12 10:51:02,766 - xfwb_generator - INFO - Processing AWB 7/20: 706-77889900
2025-06-12 10:51:02,767 - xfwb_generator - INFO - Processing AWB 8/20: 706-*************-06-12 10:51:02,767 - xfwb_generator - INFO - Processing AWB 9/20: 706-66778899
2025-06-12 10:51:02,768 - xfwb_generator - INFO - Processing AWB 10/20: 706-00112233
2025-06-12 10:51:02,768 - xfwb_generator - INFO - Processing AWB 11/20: 706-88997766
2025-06-12 10:51:02,769 - xfwb_generator - INFO - Processing AWB 12/20: 706-55443322
2025-06-12 10:51:02,769 - xfwb_generator - INFO - Processing AWB 13/20: 706-99887766
2025-06-12 10:51:02,770 - xfwb_generator - INFO - Processing AWB 14/20: 706-77665544
2025-06-12 10:51:02,770 - xfwb_generator - INFO - Processing AWB 15/20: 706-66554433
2025-06-12 10:51:02,771 - xfwb_generator - INFO - Processing AWB 16/20: 706-44556677
2025-06-12 10:51:02,771 - xfwb_generator - INFO - Processing AWB 17/20: 706-22334455
2025-06-12 10:51:02,772 - xfwb_generator - INFO - Processing AWB 18/20: 706-*************-06-12 10:51:02,772 - xfwb_generator - INFO - Processing AWB 19/20: 706-*************-06-12 10:51:02,772 - xfwb_generator - INFO - Processing AWB 20/20: 706-11223344
2025-06-12 10:51:02,773 - app - INFO - XFWB XML generated successfully, length: 96037
2025-06-12 10:51:02,773 - app - INFO - Multiple AWBs detected in the response
2025-06-12 10:51:02,773 - app - INFO - Found 20 AWBs in the response
2025-06-12 11:55:33,528 - app - INFO - Serving static file: xfwb.html
2025-06-12 11:55:34,143 - app - INFO - Serving index.html
2025-06-12 12:43:29,267 - app - INFO - Serving static file: xfzb.html
2025-06-12 12:43:31,415 - app - INFO - XFZB sample data requested
2025-06-12 12:43:31,415 - app - INFO - XFZB sample data sent
2025-06-12 12:44:11,262 - app - INFO - Received XFZB XML generation request
2025-06-12 12:44:11,262 - app - INFO - Input text length: 40
2025-06-12 12:44:11,262 - app - INFO - Generating XFZB XML...
2025-06-12 12:44:11,263 - xfzb_generator - INFO - Parsing input text: SHAS51282599/T1K4.0MC0.027/panel pc
/EAW
2025-06-12 12:44:11,263 - xfzb_generator - INFO - Found House AWB: SHAS51282599
2025-06-12 12:44:11,263 - xfzb_generator - INFO - Added Handling Instructions codes to House AWB SHAS51282599: ['EAW']
2025-06-12 12:44:11,263 - xfzb_generator - INFO - Parsed 1 House AWBs
2025-06-12 12:44:11,264 - app - INFO - XFZB XML generated successfully, length: 6168
2025-06-12 15:27:01,084 - app - INFO - Random cargo data requested
2025-06-12 15:27:01,085 - app - INFO - Random cargo data generated successfully
2025-06-13 01:23:06,618 - app - INFO - Serving static file: favicon.ico
2025-06-13 02:43:58,956 - app - INFO - Serving static file: xfwb.html
2025-06-13 08:16:44,622 - app - INFO - Serving static file: xfwb.html
2025-06-13 09:14:15,802 - app - INFO - Serving index.html
2025-06-13 09:14:17,854 - app - INFO - Serving static file: favicon.ico
2025-06-13 09:15:01,755 - app - INFO - Serving index.html
2025-06-13 11:16:17,404 - app - INFO - IATA XML Generator startup
2025-06-13 11:16:17,400 - app - INFO - IATA XML Generator startup
2025-06-13 11:16:17,407 - app - INFO - Static folder path: /var/www/xmlmaker/frontend/static
2025-06-13 11:16:17,413 - app - INFO - Static folder path: /var/www/xmlmaker/frontend/static
2025-06-13 11:16:17,413 - app - INFO - IATA XML Generator startup
2025-06-13 11:16:17,413 - app - INFO - Static folder path: /var/www/xmlmaker/frontend/static
2025-06-13 11:16:17,433 - app - INFO - IATA XML Generator startup
2025-06-13 11:16:17,433 - app - INFO - Static folder path: /var/www/xmlmaker/frontend/static
2025-06-13 11:21:27,155 - app - INFO - Serving static file: favicon.ico
2025-06-13 12:27:29,987 - app - INFO - Serving static file: xfwb.html
2025-06-13 16:07:37,460 - app - INFO - Serving index.html
2025-06-13 16:07:40,723 - app - INFO - Serving static file: xfzb.html
2025-06-13 16:07:43,789 - app - INFO - XFZB sample data requested
2025-06-13 16:07:43,789 - app - INFO - XFZB sample data sent
2025-06-13 16:07:58,022 - app - INFO - Received XFZB XML generation request
2025-06-13 16:07:58,022 - app - INFO - Input text length: 86
2025-06-13 16:07:58,022 - app - INFO - Generating XFZB XML...
2025-06-13 16:07:58,022 - xfzb_generator - INFO - Parsing input text: SHAS51282599/T1K4.0MC0.027/panel pc
/EAW
*********/T8K180.0MC0.0/MEDICAL SUPPLIES
/DGR
2025-06-13 16:07:58,022 - xfzb_generator - INFO - Found House AWB: SHAS51282599
2025-06-13 16:07:58,022 - xfzb_generator - INFO - Added Handling Instructions codes to House AWB SHAS51282599: ['EAW']
2025-06-13 16:07:58,023 - xfzb_generator - INFO - Found House AWB: *********
2025-06-13 16:07:58,023 - xfzb_generator - INFO - Added Handling Instructions codes to House AWB *********: ['DGR']
2025-06-13 16:07:58,023 - xfzb_generator - INFO - Parsed 2 House AWBs
2025-06-13 16:07:58,023 - xfzb_generator - INFO - Generating consolidated XML for 2 House AWBs
2025-06-13 16:07:58,023 - xfzb_generator - INFO - Processing House AWB 1/2: SHAS51282599
2025-06-13 16:07:58,023 - xfzb_generator - INFO - Processing House AWB 2/2: *********
2025-06-13 16:07:58,023 - app - INFO - XFZB XML generated successfully, length: 12389
2025-06-13 16:07:58,023 - app - INFO - Multiple House AWBs detected in the response
2025-06-13 16:07:58,024 - app - INFO - Found 0 House AWBs in the response
2025-06-13 16:08:42,821 - app - INFO - Received XFZB XML generation request
2025-06-13 16:08:42,821 - app - INFO - Input text length: 45
2025-06-13 16:08:42,821 - app - INFO - Generating XFZB XML...
2025-06-13 16:08:42,821 - xfzb_generator - INFO - Parsing input text: *********/T8K180.0MC0.0/MEDICAL SUPPLIES
/DGR
2025-06-13 16:08:42,822 - xfzb_generator - INFO - Found House AWB: *********
2025-06-13 16:08:42,822 - xfzb_generator - INFO - Added Handling Instructions codes to House AWB *********: ['DGR']
2025-06-13 16:08:42,822 - xfzb_generator - INFO - Parsed 1 House AWBs
2025-06-13 16:08:42,822 - app - INFO - XFZB XML generated successfully, length: 6169
2025-06-13 17:02:19,341 - app - INFO - Serving static file: xfwb.html
2025-06-13 17:02:22,517 - app - INFO - XFWB sample data requested
2025-06-13 17:02:22,518 - app - INFO - XFWB sample data sent
2025-06-13 17:28:40,551 - app - INFO - Serving static file: xfzb.html
2025-06-13 17:28:40,762 - app - INFO - Serving index.html
2025-06-13 17:31:14,184 - app - INFO - IATA XML Generator startup
2025-06-13 17:31:14,184 - app - INFO - Static folder path: /var/www/xmlmaker/frontend/static
2025-06-13 17:31:14,189 - app - INFO - Starting server on port 5002
2025-06-13 17:31:14,226 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-06-13 17:31:14,226 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-13 17:31:14,230 - werkzeug - INFO -  * Restarting with stat
2025-06-13 17:31:14,705 - app - INFO - IATA XML Generator startup
2025-06-13 17:31:14,706 - app - INFO - Static folder path: /var/www/xmlmaker/frontend/static
2025-06-13 17:31:14,713 - app - INFO - Starting server on port 5002
2025-06-13 17:31:14,720 - werkzeug - WARNING -  * Debugger is active!
2025-06-13 17:31:14,736 - werkzeug - INFO -  * Debugger PIN: 214-739-885
2025-06-13 17:31:31,471 - app - INFO - Received XFZB XML generation request
2025-06-13 17:31:31,472 - app - INFO - Input text length: 89
2025-06-13 17:31:31,474 - app - INFO - Generating XFZB XML...
2025-06-13 17:31:31,475 - xfzb_generator - INFO - Parsing input text: SHA123|706-51663054|KQ7001|2014-07-07T11:38:25|1|10|10.0|1.01|DXB-LLW|LAPTOPS BOX|ECC/EAP
2025-06-13 17:31:31,477 - xfzb_generator - INFO - Parsed HAWB: SHA123, MAWB: 706-51663054, Route: DXB-LLW
2025-06-13 17:31:31,478 - app - INFO - XFZB XML generated successfully, length: 5797
2025-06-13 17:31:31,479 - werkzeug - INFO - 127.0.0.1 - - [13/Jun/2025 17:31:31] "POST /api/generate-xfzb HTTP/1.1" 200 -
2025-06-13 17:31:51,973 - app - INFO - Serving static file: xfzb.html
2025-06-13 17:31:51,993 - werkzeug - INFO - 127.0.0.1 - - [13/Jun/2025 17:31:51] "GET /xfzb.html HTTP/1.1" 200 -
2025-06-13 17:31:53,267 - app - INFO - Serving static file: favicon.ico
2025-06-13 17:31:53,268 - werkzeug - INFO - 127.0.0.1 - - [13/Jun/2025 17:31:53] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-06-13 17:31:56,776 - app - INFO - Serving static file: xfzb.html
2025-06-13 17:31:56,776 - werkzeug - INFO - 127.0.0.1 - - [13/Jun/2025 17:31:56] "[36mGET /xfzb.html HTTP/1.1[0m" 304 -
2025-06-13 17:34:56,391 - app - INFO - Serving static file: xfwb.html
2025-06-13 17:34:59,543 - app - INFO - XFWB sample data requested
2025-06-13 17:34:59,543 - app - INFO - XFWB sample data sent
2025-06-13 17:35:06,111 - app - INFO - Received XFWB XML generation request
2025-06-13 17:35:06,111 - app - INFO - Input text length: 102
2025-06-13 17:35:06,111 - app - INFO - Generating XFWB XML...
2025-06-13 17:35:06,111 - xfwb_generator - INFO - Parsing input text: 706-51663054NBOLLW/T1K138.0MC1.01/HUMAN REMAINS
/HUM
706-60609942NBOLLW/T4K25.0MC0.01/DHL EXPRESS
/COU
2025-06-13 17:35:06,112 - xfwb_generator - INFO - Found AWB: 706-51663054
2025-06-13 17:35:06,112 - xfwb_generator - INFO - Added Handling Instructions codes to AWB 706-51663054: ['HUM']
2025-06-13 17:35:06,112 - xfwb_generator - INFO - Found AWB: 706-60609942
2025-06-13 17:35:06,112 - xfwb_generator - INFO - Added Handling Instructions codes to AWB 706-60609942: ['COU']
2025-06-13 17:35:06,112 - xfwb_generator - INFO - Parsed 2 AWBs
2025-06-13 17:35:06,112 - xfwb_generator - INFO - Generating consolidated XML for 2 AWBs
2025-06-13 17:35:06,112 - xfwb_generator - INFO - Processing AWB 1/2: 706-51663054
2025-06-13 17:35:06,113 - xfwb_generator - INFO - Processing AWB 2/2: 706-60609942
2025-06-13 17:35:06,113 - app - INFO - XFWB XML generated successfully, length: 9452
2025-06-13 17:35:06,113 - app - INFO - Multiple AWBs detected in the response
2025-06-13 17:35:06,113 - app - INFO - Found 2 AWBs in the response
2025-06-13 18:22:42,928 - app - INFO - Serving static file: xfwb.html
2025-06-14 01:56:03,880 - app - INFO - Serving static file: xfzb.html
2025-06-14 01:56:05,996 - app - INFO - Serving static file: favicon.ico
2025-06-14 07:12:02,392 - app - INFO - Serving index.html
2025-06-14 07:12:04,963 - app - INFO - Serving static file: xfzb.html
2025-06-14 07:12:09,245 - app - INFO - XFZB sample data requested
2025-06-14 07:12:09,245 - app - INFO - XFZB sample data sent
2025-06-14 07:12:22,493 - app - INFO - Received XFZB XML generation request
2025-06-14 07:12:22,501 - app - INFO - Input text length: 86
2025-06-14 07:12:22,502 - app - INFO - Generating XFZB XML...
2025-06-14 07:12:22,502 - xfzb_generator - INFO - Parsing input text: SHAS51282599/T1K4.0MC0.027/panel pc
/EAW
*********/T8K180.0MC0.0/MEDICAL SUPPLIES
/DGR
2025-06-14 07:12:22,502 - xfzb_generator - INFO - Found House AWB: SHAS51282599
2025-06-14 07:12:22,502 - xfzb_generator - INFO - Added Handling Instructions codes to House AWB SHAS51282599: ['EAW']
2025-06-14 07:12:22,502 - xfzb_generator - INFO - Found House AWB: *********
2025-06-14 07:12:22,502 - xfzb_generator - INFO - Added Handling Instructions codes to House AWB *********: ['DGR']
2025-06-14 07:12:22,502 - xfzb_generator - INFO - Parsed 2 House AWBs
2025-06-14 07:12:22,503 - xfzb_generator - INFO - Generating consolidated XML for 2 House AWBs
2025-06-14 07:12:22,503 - xfzb_generator - INFO - Processing House AWB 1/2: SHAS51282599
2025-06-14 07:12:22,504 - xfzb_generator - INFO - Processing House AWB 2/2: *********
2025-06-14 07:12:22,505 - app - INFO - XFZB XML generated successfully, length: 12389
2025-06-14 07:12:22,505 - app - INFO - Multiple House AWBs detected in the response
2025-06-14 07:12:22,506 - app - INFO - Found 0 House AWBs in the response
2025-06-14 07:12:41,713 - app - INFO - Serving static file: xfzb.html
2025-06-14 07:12:42,602 - app - INFO - Serving static file: favicon.ico
2025-06-14 07:12:45,779 - app - INFO - Serving static file: xfzb.html
2025-06-14 07:12:52,072 - app - INFO - XFZB sample data requested
2025-06-14 07:12:52,072 - app - INFO - XFZB sample data sent
2025-06-14 07:12:55,538 - app - INFO - Received XFZB XML generation request
2025-06-14 07:12:55,538 - app - INFO - Input text length: 86
2025-06-14 07:12:55,538 - app - INFO - Generating XFZB XML...
2025-06-14 07:12:55,538 - xfzb_generator - INFO - Parsing input text: SHAS51282599/T1K4.0MC0.027/panel pc
/EAW
*********/T8K180.0MC0.0/MEDICAL SUPPLIES
/DGR
2025-06-14 07:12:55,538 - xfzb_generator - INFO - Found House AWB: SHAS51282599
2025-06-14 07:12:55,538 - xfzb_generator - INFO - Added Handling Instructions codes to House AWB SHAS51282599: ['EAW']
2025-06-14 07:12:55,538 - xfzb_generator - INFO - Found House AWB: *********
2025-06-14 07:12:55,538 - xfzb_generator - INFO - Added Handling Instructions codes to House AWB *********: ['DGR']
2025-06-14 07:12:55,538 - xfzb_generator - INFO - Parsed 2 House AWBs
2025-06-14 07:12:55,538 - xfzb_generator - INFO - Generating consolidated XML for 2 House AWBs
2025-06-14 07:12:55,538 - xfzb_generator - INFO - Processing House AWB 1/2: SHAS51282599
2025-06-14 07:12:55,539 - xfzb_generator - INFO - Processing House AWB 2/2: *********
2025-06-14 07:12:55,540 - app - INFO - XFZB XML generated successfully, length: 12389
2025-06-14 07:12:55,540 - app - INFO - Multiple House AWBs detected in the response
2025-06-14 07:12:55,540 - app - INFO - Found 0 House AWBs in the response
2025-06-14 07:19:56,503 - app - INFO - Serving static file: xfzb.html
2025-06-14 07:19:58,714 - app - INFO - Serving static file: xfzb.html
2025-06-14 07:20:02,954 - app - INFO - XFZB sample data requested
2025-06-14 07:20:02,954 - app - INFO - XFZB sample data sent
2025-06-14 07:20:07,721 - app - INFO - Received XFZB XML generation request
2025-06-14 07:20:07,721 - app - INFO - Input text length: 86
2025-06-14 07:20:07,722 - app - INFO - Generating XFZB XML...
2025-06-14 07:20:07,722 - xfzb_generator - INFO - Parsing input text: SHAS51282599/T1K4.0MC0.027/panel pc
/EAW
*********/T8K180.0MC0.0/MEDICAL SUPPLIES
/DGR
2025-06-14 07:20:07,722 - xfzb_generator - INFO - Found House AWB: SHAS51282599
2025-06-14 07:20:07,722 - xfzb_generator - INFO - Added Handling Instructions codes to House AWB SHAS51282599: ['EAW']
2025-06-14 07:20:07,722 - xfzb_generator - INFO - Found House AWB: *********
2025-06-14 07:20:07,722 - xfzb_generator - INFO - Added Handling Instructions codes to House AWB *********: ['DGR']
2025-06-14 07:20:07,722 - xfzb_generator - INFO - Parsed 2 House AWBs
2025-06-14 07:20:07,722 - xfzb_generator - INFO - Generating consolidated XML for 2 House AWBs
2025-06-14 07:20:07,722 - xfzb_generator - INFO - Processing House AWB 1/2: SHAS51282599
2025-06-14 07:20:07,722 - xfzb_generator - INFO - Processing House AWB 2/2: *********
2025-06-14 07:20:07,723 - app - INFO - XFZB XML generated successfully, length: 12389
2025-06-14 07:20:07,723 - app - INFO - Multiple House AWBs detected in the response
2025-06-14 07:20:07,723 - app - INFO - Found 0 House AWBs in the response
2025-06-14 07:21:20,883 - app - INFO - Received XFZB XML generation request
2025-06-14 07:21:20,884 - app - INFO - Input text length: 89
2025-06-14 07:21:20,885 - app - INFO - Generating XFZB XML...
2025-06-14 07:21:20,885 - xfzb_generator - INFO - Parsing input text: SHA123|706-51663054|KQ7001|2014-07-07T11:38:25|1|10|10.0|1.01|DXB-LLW|LAPTOPS BOX|ECC/EAP
2025-06-14 07:21:20,885 - xfzb_generator - WARNING - No valid House AWBs found in the input text
2025-06-14 07:21:20,885 - xfzb_generator - WARNING - No House AWBs found to generate XML
2025-06-14 07:21:20,885 - app - ERROR - Error in XFZB generation: No valid House AWBs found to generate XML
2025-06-14 07:21:28,179 - app - INFO - Received XFZB XML generation request
2025-06-14 07:21:28,181 - app - INFO - Input text length: 89
2025-06-14 07:21:28,182 - app - INFO - Generating XFZB XML...
2025-06-14 07:21:28,183 - xfzb_generator - INFO - Parsing input text: SHA123|706-51663054|KQ7001|2014-07-07T11:38:25|1|10|10.0|1.01|DXB-LLW|LAPTOPS BOX|ECC/EAP
2025-06-14 07:21:28,183 - xfzb_generator - WARNING - No valid House AWBs found in the input text
2025-06-14 07:21:28,184 - xfzb_generator - WARNING - No House AWBs found to generate XML
2025-06-14 07:21:28,184 - app - ERROR - Error in XFZB generation: No valid House AWBs found to generate XML
2025-06-14 07:28:06,780 - app - INFO - IATA XML Generator startup
2025-06-14 07:28:06,781 - app - INFO - Static folder path: /var/www/xmlmaker/frontend/static
2025-06-14 07:28:06,779 - app - INFO - IATA XML Generator startup
2025-06-14 07:28:06,783 - app - INFO - Static folder path: /var/www/xmlmaker/frontend/static
2025-06-14 07:28:06,786 - app - INFO - IATA XML Generator startup
2025-06-14 07:28:06,788 - app - INFO - IATA XML Generator startup
2025-06-14 07:28:06,788 - app - INFO - Static folder path: /var/www/xmlmaker/frontend/static
2025-06-14 07:28:06,792 - app - INFO - Static folder path: /var/www/xmlmaker/frontend/static
2025-06-14 07:28:46,217 - app - INFO - Serving static file: xfzb.html
2025-06-14 07:28:49,356 - app - INFO - Serving static file: xfzb.html
2025-06-14 07:28:51,186 - app - INFO - Serving static file: xfzb.html
2025-06-14 07:29:13,814 - app - INFO - Serving static file: xfzb.html
2025-06-14 07:29:19,452 - app - INFO - XFZB sample data requested
2025-06-14 07:29:19,453 - app - INFO - XFZB sample data sent
2025-06-14 07:29:26,230 - app - INFO - Received XFZB XML generation request
2025-06-14 07:29:26,231 - app - INFO - Input text length: 86
2025-06-14 07:29:26,231 - app - INFO - Generating XFZB XML...
2025-06-14 07:29:26,231 - xfzb_generator - INFO - Parsing input text: SHAS51282599/T1K4.0MC0.027/panel pc
/EAW
*********/T8K180.0MC0.0/MEDICAL SUPPLIES
/DGR
2025-06-14 07:29:26,232 - xfzb_generator - ERROR - Error parsing house waybill info: Expected 11 pipe-delimited fields, got 1
Traceback (most recent call last):
  File "/var/www/xmlmaker/xfzb_generator.py", line 96, in _parse_house_waybill_info
    raise ValueError(f"Expected 11 pipe-delimited fields, got {len(parts)}")
ValueError: Expected 11 pipe-delimited fields, got 1
2025-06-14 07:29:26,233 - xfzb_generator - ERROR - Error creating XFZB XML: Expected 11 pipe-delimited fields, got 1
Traceback (most recent call last):
  File "/var/www/xmlmaker/xfzb_generator.py", line 58, in create_house_waybill_xml
    house_waybill_info = self._parse_house_waybill_info(input_text)
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/var/www/xmlmaker/xfzb_generator.py", line 96, in _parse_house_waybill_info
    raise ValueError(f"Expected 11 pipe-delimited fields, got {len(parts)}")
ValueError: Expected 11 pipe-delimited fields, got 1
2025-06-14 07:29:26,233 - app - ERROR - Error in XFZB generation: Error creating XFZB XML: Expected 11 pipe-delimited fields, got 1
2025-06-14 07:30:19,790 - app - INFO - Received XFZB XML generation request
2025-06-14 07:30:19,790 - app - INFO - Input text length: 89
2025-06-14 07:30:19,790 - app - INFO - Generating XFZB XML...
2025-06-14 07:30:19,791 - xfzb_generator - INFO - Parsing input text: SHA123|706-51663054|KQ7001|2014-07-07T11:38:25|1|10|10.0|1.01|DXB-LLW|LAPTOPS BOX|ECC/EAP
2025-06-14 07:30:19,792 - xfzb_generator - INFO - Parsed HAWB: SHA123, MAWB: 706-51663054, Route: DXB-LLW
2025-06-14 07:30:19,792 - app - INFO - XFZB XML generated successfully, length: 5797
2025-06-14 07:30:29,922 - app - INFO - Received XFZB XML generation request
2025-06-14 07:30:29,923 - app - INFO - Input text length: 179
2025-06-14 07:30:29,923 - app - INFO - Generating XFZB XML...
2025-06-14 07:30:29,924 - xfzb_generator - INFO - Parsing input text: SHA123|706-51663054|KQ7001|2014-07-07T11:38:25|1|10|10.0|1.01|DXB-LLW|LAPTOPS BOX|ECC/EAP
SHA123|706-51663054|KQ7001|2014-07-07T11:38:25|1|10|10.0|1.01|DXB-LLW|LAPTOPS BOX|ECC/EAP
2025-06-14 07:30:29,924 - xfzb_generator - ERROR - Error parsing house waybill info: Expected 11 pipe-delimited fields, got 21
Traceback (most recent call last):
  File "/var/www/xmlmaker/xfzb_generator.py", line 96, in _parse_house_waybill_info
    raise ValueError(f"Expected 11 pipe-delimited fields, got {len(parts)}")
ValueError: Expected 11 pipe-delimited fields, got 21
2025-06-14 07:30:29,925 - xfzb_generator - ERROR - Error creating XFZB XML: Expected 11 pipe-delimited fields, got 21
Traceback (most recent call last):
  File "/var/www/xmlmaker/xfzb_generator.py", line 58, in create_house_waybill_xml
    house_waybill_info = self._parse_house_waybill_info(input_text)
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/var/www/xmlmaker/xfzb_generator.py", line 96, in _parse_house_waybill_info
    raise ValueError(f"Expected 11 pipe-delimited fields, got {len(parts)}")
ValueError: Expected 11 pipe-delimited fields, got 21
2025-06-14 07:30:29,926 - app - ERROR - Error in XFZB generation: Error creating XFZB XML: Expected 11 pipe-delimited fields, got 21
2025-06-14 08:33:05,556 - app - INFO - Serving static file: xfzb.html
2025-06-14 08:33:05,924 - app - INFO - Serving index.html
2025-06-14 08:56:13,738 - app - INFO - Serving static file: index.html
2025-06-14 08:56:17,847 - app - INFO - Serving static file: index.html
2025-06-14 08:56:26,621 - app - INFO - Received Flight Manifest XML generation request
2025-06-14 08:56:26,622 - app - INFO - Input text length: 2093
2025-06-14 08:56:26,622 - app - INFO - Generating Flight Manifest XML...
2025-06-14 08:56:26,622 - app - INFO - Processing 74 lines of input
2025-06-14 08:56:26,622 - app - INFO - Processing line: FFM/8
2025-06-14 08:56:26,623 - app - INFO - Processing line: 1/KQ2738/13JUN/NBO/5Y-KQC
2025-06-14 08:56:26,623 - app - INFO - Processing line: LLW
2025-06-14 08:56:26,623 - app - INFO - Processing line: 706-51652742NBOLLW/S2K70.0MC0.16T10/TACKING SCREWS
2025-06-14 08:56:26,623 - app - INFO - Found AWB line: 706-51652742NBOLLW/S2K70.0MC0.16T10/TACKING SCREWS
2025-06-14 08:56:26,624 - app - INFO - Adding AWB: {'prefix': '706', 'number': '51652742', 'origin': 'NBO', 'destination': 'LLW', 'split_code': 'S', 'pieces': '2', 'weight': '70.0', 'volume': '0.16', 'description': 'TACKING SCREWS', 'special_codes': [], 'uld': None}
2025-06-14 08:56:26,624 - app - INFO - Processing line: /EAP/GEN
2025-06-14 08:56:26,625 - app - INFO - Found special code line: /EAP/GEN
2025-06-14 08:56:26,625 - app - INFO - Processing line: ULD/PAJ33019KQ
2025-06-14 08:56:26,625 - app - INFO - Found ULD line: ULD/PAJ33019KQ
2025-06-14 08:56:26,625 - app - INFO - Adding ULD: {'type': 'PAJ', 'number': '33019', 'airline': 'KQ', 'awbs': [], 'origin': 'DWC'}
2025-06-14 08:56:26,626 - app - INFO - Processing line: 706-51764053BOMLLW/D33K833.0MC1.33T220/NCARB
2025-06-14 08:56:26,626 - app - INFO - Found AWB line: 706-51764053BOMLLW/D33K833.0MC1.33T220/NCARB
2025-06-14 08:56:26,626 - app - WARNING - Could not parse weight/volume from: D33K833.0MC1.33T220
2025-06-14 08:56:26,626 - app - INFO - Adding AWB: {'prefix': '706', 'number': '51764053', 'origin': 'BOM', 'destination': 'LLW', 'split_code': 'T', 'pieces': '1', 'weight': '0', 'volume': '0', 'description': 'NCARB', 'special_codes': [], 'uld': 0}
2025-06-14 08:56:26,627 - app - INFO - Processing line: /GEN
2025-06-14 08:56:26,627 - app - INFO - Found special code line: /GEN
2025-06-14 08:56:26,627 - app - INFO - Processing line: 706-51653630NBOLLW/S16K176.0MC1.40T37/NIPCAP STD AFRI
2025-06-14 08:56:26,627 - app - INFO - Found AWB line: 706-51653630NBOLLW/S16K176.0MC1.40T37/NIPCAP STD AFRI
2025-06-14 08:56:26,627 - app - INFO - Adding AWB: {'prefix': '706', 'number': '51653630', 'origin': 'NBO', 'destination': 'LLW', 'split_code': 'S', 'pieces': '16', 'weight': '176.0', 'volume': '1.40', 'description': 'NIPCAP STD AFRI', 'special_codes': [], 'uld': 0}
2025-06-14 08:56:26,627 - app - INFO - Processing line: /GEN
2025-06-14 08:56:26,628 - app - INFO - Found special code line: /GEN
2025-06-14 08:56:26,628 - app - INFO - Processing line: 706-51168272JNBLLW/T1K1350.0MC3.35/SPARES PARTS
2025-06-14 08:56:26,628 - app - INFO - Found AWB line: 706-51168272JNBLLW/T1K1350.0MC3.35/SPARES PARTS
2025-06-14 08:56:26,628 - app - INFO - Adding AWB: {'prefix': '706', 'number': '51168272', 'origin': 'JNB', 'destination': 'LLW', 'split_code': 'T', 'pieces': '1', 'weight': '1350.0', 'volume': '3.35', 'description': 'SPARES PARTS', 'special_codes': [], 'uld': 0}
2025-06-14 08:56:26,628 - app - INFO - Processing line: /GEN
2025-06-14 08:56:26,628 - app - INFO - Found special code line: /GEN
2025-06-14 08:56:26,628 - app - INFO - Processing line: 574-34424036BRULLW/T1K132.0MC0.79/PHARMA
2025-06-14 08:56:26,629 - app - INFO - Found AWB line: 574-34424036BRULLW/T1K132.0MC0.79/PHARMA
2025-06-14 08:56:26,629 - app - INFO - Adding AWB: {'prefix': '574', 'number': '34424036', 'origin': 'BRU', 'destination': 'LLW', 'split_code': 'T', 'pieces': '1', 'weight': '132.0', 'volume': '0.79', 'description': 'PHARMA', 'special_codes': [], 'uld': 0}
2025-06-14 08:56:26,629 - app - INFO - Processing line: ULD/PAJ33162KQ
2025-06-14 08:56:26,629 - app - INFO - Found ULD line: ULD/PAJ33162KQ
2025-06-14 08:56:26,629 - app - INFO - Adding ULD: {'type': 'PAJ', 'number': '33162', 'airline': 'KQ', 'awbs': [], 'origin': 'DWC'}
2025-06-14 08:56:26,630 - app - INFO - Processing line: 706-51764053BOMLLW/D140K3535.0MC5.64T220/NCARB
2025-06-14 08:56:26,630 - app - INFO - Found AWB line: 706-51764053BOMLLW/D140K3535.0MC5.64T220/NCARB
2025-06-14 08:56:26,630 - app - WARNING - Could not parse weight/volume from: D140K3535.0MC5.64T220
2025-06-14 08:56:26,630 - app - INFO - Adding AWB: {'prefix': '706', 'number': '51764053', 'origin': 'BOM', 'destination': 'LLW', 'split_code': 'T', 'pieces': '1', 'weight': '0', 'volume': '0', 'description': 'NCARB', 'special_codes': [], 'uld': 1}
2025-06-14 08:56:26,631 - app - INFO - Processing line: /GEN
2025-06-14 08:56:26,631 - app - INFO - Found special code line: /GEN
2025-06-14 08:56:26,631 - app - INFO - Processing line: ULD/PAJ33167KQ
2025-06-14 08:56:26,631 - app - INFO - Found ULD line: ULD/PAJ33167KQ
2025-06-14 08:56:26,631 - app - INFO - Adding ULD: {'type': 'PAJ', 'number': '33167', 'airline': 'KQ', 'awbs': [], 'origin': 'DWC'}
2025-06-14 08:56:26,631 - app - INFO - Processing line: 706-51663463NBOLLW/T1K26.0MC0.04/DRILLING MUD SA
2025-06-14 08:56:26,631 - app - INFO - Found AWB line: 706-51663463NBOLLW/T1K26.0MC0.04/DRILLING MUD SA
2025-06-14 08:56:26,631 - app - INFO - Adding AWB: {'prefix': '706', 'number': '51663463', 'origin': 'NBO', 'destination': 'LLW', 'split_code': 'T', 'pieces': '1', 'weight': '26.0', 'volume': '0.04', 'description': 'DRILLING MUD SA', 'special_codes': [], 'uld': 2}
2025-06-14 08:56:26,632 - app - INFO - Processing line: /GEN
2025-06-14 08:56:26,632 - app - INFO - Found special code line: /GEN
2025-06-14 08:56:26,632 - app - INFO - Processing line: 706-51722101LHRLLW/T2K36.0MC0.23/OPHTHALMOLOGY E
2025-06-14 08:56:26,632 - app - INFO - Found AWB line: 706-51722101LHRLLW/T2K36.0MC0.23/OPHTHALMOLOGY E
2025-06-14 08:56:26,632 - app - INFO - Adding AWB: {'prefix': '706', 'number': '51722101', 'origin': 'LHR', 'destination': 'LLW', 'split_code': 'T', 'pieces': '2', 'weight': '36.0', 'volume': '0.23', 'description': 'OPHTHALMOLOGY E', 'special_codes': [], 'uld': 2}
2025-06-14 08:56:26,633 - app - INFO - Processing line: 706-51653630NBOLLW/S21K234.0MC1.84T37/NIPCAP STD AFRI
2025-06-14 08:56:26,633 - app - INFO - Found AWB line: 706-51653630NBOLLW/S21K234.0MC1.84T37/NIPCAP STD AFRI
2025-06-14 08:56:26,633 - app - INFO - Adding AWB: {'prefix': '706', 'number': '51653630', 'origin': 'NBO', 'destination': 'LLW', 'split_code': 'S', 'pieces': '21', 'weight': '234.0', 'volume': '1.84', 'description': 'NIPCAP STD AFRI', 'special_codes': [], 'uld': 2}
2025-06-14 08:56:26,633 - app - INFO - Processing line: /GEN
2025-06-14 08:56:26,634 - app - INFO - Found special code line: /GEN
2025-06-14 08:56:26,634 - app - INFO - Processing line: 706-51689691FRABLZ/T1K26.0MC0.16/MED.DIAGN.PRODU
2025-06-14 08:56:26,634 - app - INFO - Found AWB line: 706-51689691FRABLZ/T1K26.0MC0.16/MED.DIAGN.PRODU
2025-06-14 08:56:26,634 - app - INFO - Adding AWB: {'prefix': '706', 'number': '51689691', 'origin': 'FRA', 'destination': 'BLZ', 'split_code': 'T', 'pieces': '1', 'weight': '26.0', 'volume': '0.16', 'description': 'MED.DIAGN.PRODU', 'special_codes': [], 'uld': 2}
2025-06-14 08:56:26,634 - app - INFO - Processing line: /SPX
2025-06-14 08:56:26,634 - app - INFO - Found special code line: /SPX
2025-06-14 08:56:26,635 - app - INFO - Processing line: 485-04706332CANLLW/P4K100.0MC0.01T25/SHOE MATEREAL
2025-06-14 08:56:26,635 - app - INFO - Found AWB line: 485-04706332CANLLW/P4K100.0MC0.01T25/SHOE MATEREAL
2025-06-14 08:56:26,636 - app - INFO - Adding AWB: {'prefix': '485', 'number': '04706332', 'origin': 'CAN', 'destination': 'LLW', 'split_code': 'P', 'pieces': '4', 'weight': '100.0', 'volume': '0.01', 'description': 'SHOE MATEREAL', 'special_codes': [], 'uld': 2}
2025-06-14 08:56:26,636 - app - INFO - Processing line: 706-51667346AMSLLW/T14K374.9MC1.54/KYOCERA MFPS.
2025-06-14 08:56:26,636 - app - INFO - Found AWB line: 706-51667346AMSLLW/T14K374.9MC1.54/KYOCERA MFPS.
2025-06-14 08:56:26,636 - app - INFO - Adding AWB: {'prefix': '706', 'number': '51667346', 'origin': 'AMS', 'destination': 'LLW', 'split_code': 'T', 'pieces': '14', 'weight': '374.9', 'volume': '1.54', 'description': 'KYOCERA MFPS.', 'special_codes': [], 'uld': 2}
2025-06-14 08:56:26,636 - app - INFO - Processing line: /SPX
2025-06-14 08:56:26,636 - app - INFO - Found special code line: /SPX
2025-06-14 08:56:26,636 - app - INFO - Processing line: 485-09319041MIALLW/T2K241.0MC1.45/MEDICAL EQUIPME
2025-06-14 08:56:26,637 - app - INFO - Found AWB line: 485-09319041MIALLW/T2K241.0MC1.45/MEDICAL EQUIPME
2025-06-14 08:56:26,637 - app - INFO - Adding AWB: {'prefix': '485', 'number': '09319041', 'origin': 'MIA', 'destination': 'LLW', 'split_code': 'T', 'pieces': '2', 'weight': '241.0', 'volume': '1.45', 'description': 'MEDICAL EQUIPME', 'special_codes': [], 'uld': 2}
2025-06-14 08:56:26,637 - app - INFO - Processing line: ULD/PAJ33219KQ
2025-06-14 08:56:26,637 - app - INFO - Found ULD line: ULD/PAJ33219KQ
2025-06-14 08:56:26,637 - app - INFO - Adding ULD: {'type': 'PAJ', 'number': '33219', 'airline': 'KQ', 'awbs': [], 'origin': 'DWC'}
2025-06-14 08:56:26,637 - app - INFO - Processing line: 706-51684006JNBLLW/T1K185.0MC0.71/STC SPARES
2025-06-14 08:56:26,637 - app - INFO - Found AWB line: 706-51684006JNBLLW/T1K185.0MC0.71/STC SPARES
2025-06-14 08:56:26,637 - app - INFO - Adding AWB: {'prefix': '706', 'number': '51684006', 'origin': 'JNB', 'destination': 'LLW', 'split_code': 'T', 'pieces': '1', 'weight': '185.0', 'volume': '0.71', 'description': 'STC SPARES', 'special_codes': [], 'uld': 3}
2025-06-14 08:56:26,638 - app - INFO - Processing line: /GEN
2025-06-14 08:56:26,638 - app - INFO - Found special code line: /GEN
2025-06-14 08:56:26,638 - app - INFO - Processing line: 706-51764053BOMLLW/D34K858.0MC1.37T220/NCARB
2025-06-14 08:56:26,638 - app - INFO - Found AWB line: 706-51764053BOMLLW/D34K858.0MC1.37T220/NCARB
2025-06-14 08:56:26,638 - app - WARNING - Could not parse weight/volume from: D34K858.0MC1.37T220
2025-06-14 08:56:26,638 - app - INFO - Adding AWB: {'prefix': '706', 'number': '51764053', 'origin': 'BOM', 'destination': 'LLW', 'split_code': 'T', 'pieces': '1', 'weight': '0', 'volume': '0', 'description': 'NCARB', 'special_codes': [], 'uld': 3}
2025-06-14 08:56:26,638 - app - INFO - Processing line: /GEN
2025-06-14 08:56:26,638 - app - INFO - Found special code line: /GEN
2025-06-14 08:56:26,638 - app - INFO - Processing line: 706-51652742NBOLLW/S8K168.0MC0.62T10/TACKING SCREWS
2025-06-14 08:56:26,638 - app - INFO - Found AWB line: 706-51652742NBOLLW/S8K168.0MC0.62T10/TACKING SCREWS
2025-06-14 08:56:26,638 - app - INFO - Adding AWB: {'prefix': '706', 'number': '51652742', 'origin': 'NBO', 'destination': 'LLW', 'split_code': 'S', 'pieces': '8', 'weight': '168.0', 'volume': '0.62', 'description': 'TACKING SCREWS', 'special_codes': [], 'uld': 3}
2025-06-14 08:56:26,638 - app - INFO - Processing line: /EAP/GEN
2025-06-14 08:56:26,638 - app - INFO - Found special code line: /EAP/GEN
2025-06-14 08:56:26,639 - app - INFO - Processing line: 706-51616250NBOLLW/P75K824.1MC3.15T85/SELF ADHESIVE T
2025-06-14 08:56:26,639 - app - INFO - Found AWB line: 706-51616250NBOLLW/P75K824.1MC3.15T85/SELF ADHESIVE T
2025-06-14 08:56:26,639 - app - INFO - Adding AWB: {'prefix': '706', 'number': '51616250', 'origin': 'NBO', 'destination': 'LLW', 'split_code': 'P', 'pieces': '75', 'weight': '824.1', 'volume': '3.15', 'description': 'SELF ADHESIVE T', 'special_codes': [], 'uld': 3}
2025-06-14 08:56:26,639 - app - INFO - Processing line: /EAP/GEN
2025-06-14 08:56:26,639 - app - INFO - Found special code line: /EAP/GEN
2025-06-14 08:56:26,639 - app - INFO - Processing line: 574-34400612ATLLLW/T1K77.0MC0.46/PA THERMAL TRAN
2025-06-14 08:56:26,639 - app - INFO - Found AWB line: 574-34400612ATLLLW/T1K77.0MC0.46/PA THERMAL TRAN
2025-06-14 08:56:26,639 - app - INFO - Adding AWB: {'prefix': '574', 'number': '34400612', 'origin': 'ATL', 'destination': 'LLW', 'split_code': 'T', 'pieces': '1', 'weight': '77.0', 'volume': '0.46', 'description': 'PA THERMAL TRAN', 'special_codes': [], 'uld': 3}
2025-06-14 08:56:26,639 - app - INFO - Processing line: 529-40087165NBOBLZ/T3K154.0MC0.23/PEDROLLO BC75 P
2025-06-14 08:56:26,639 - app - INFO - Found AWB line: 529-40087165NBOBLZ/T3K154.0MC0.23/PEDROLLO BC75 P
2025-06-14 08:56:26,640 - app - INFO - Adding AWB: {'prefix': '529', 'number': '40087165', 'origin': 'NBO', 'destination': 'BLZ', 'split_code': 'T', 'pieces': '3', 'weight': '154.0', 'volume': '0.23', 'description': 'PEDROLLO BC75 P', 'special_codes': [], 'uld': 3}
2025-06-14 08:56:26,640 - app - INFO - Processing line: /GEN
2025-06-14 08:56:26,640 - app - INFO - Found special code line: /GEN
2025-06-14 08:56:26,640 - app - INFO - Processing line: 706-51763961DELLLW/P52K723.0MC4.33T63/PHARMACEUTICALS
2025-06-14 08:56:26,640 - app - INFO - Found AWB line: 706-51763961DELLLW/P52K723.0MC4.33T63/PHARMACEUTICALS
2025-06-14 08:56:26,640 - app - INFO - Adding AWB: {'prefix': '706', 'number': '51763961', 'origin': 'DEL', 'destination': 'LLW', 'split_code': 'P', 'pieces': '52', 'weight': '723.0', 'volume': '4.33', 'description': 'PHARMACEUTICALS', 'special_codes': [], 'uld': 3}
2025-06-14 08:56:26,640 - app - INFO - Processing line: /GEN
2025-06-14 08:56:26,640 - app - INFO - Found special code line: /GEN
2025-06-14 08:56:26,640 - app - INFO - Processing line: ULD/PAG14855KL
2025-06-14 08:56:26,640 - app - INFO - Found ULD line: ULD/PAG14855KL
2025-06-14 08:56:26,640 - app - INFO - Adding ULD: {'type': 'PAG', 'number': '14855', 'airline': 'KL', 'awbs': [], 'origin': 'DWC'}
2025-06-14 08:56:26,640 - app - INFO - Processing line: 706-51736484LHRLLW/T1K100.0MC0.60/PERSONAL EFFECT
2025-06-14 08:56:26,640 - app - INFO - Found AWB line: 706-51736484LHRLLW/T1K100.0MC0.60/PERSONAL EFFECT
2025-06-14 08:56:26,640 - app - INFO - Adding AWB: {'prefix': '706', 'number': '51736484', 'origin': 'LHR', 'destination': 'LLW', 'split_code': 'T', 'pieces': '1', 'weight': '100.0', 'volume': '0.60', 'description': 'PERSONAL EFFECT', 'special_codes': [], 'uld': 4}
2025-06-14 08:56:26,641 - app - INFO - Processing line: 706-51677161LINLLW/T1K170.0MC1.21/SPARE TORO 25 F
2025-06-14 08:56:26,641 - app - INFO - Found AWB line: 706-51677161LINLLW/T1K170.0MC1.21/SPARE TORO 25 F
2025-06-14 08:56:26,641 - app - INFO - Adding AWB: {'prefix': '706', 'number': '51677161', 'origin': 'LIN', 'destination': 'LLW', 'split_code': 'T', 'pieces': '1', 'weight': '170.0', 'volume': '1.21', 'description': 'SPARE TORO 25 F', 'special_codes': [], 'uld': 4}
2025-06-14 08:56:26,641 - app - INFO - Processing line: /GEN
2025-06-14 08:56:26,642 - app - INFO - Found special code line: /GEN
2025-06-14 08:56:26,642 - app - INFO - Processing line: 706-51682923JNBLLW/S3K162.0MC0.90T12/AUTOMOTIVE SPAR
2025-06-14 08:56:26,642 - app - INFO - Found AWB line: 706-51682923JNBLLW/S3K162.0MC0.90T12/AUTOMOTIVE SPAR
2025-06-14 08:56:26,642 - app - INFO - Adding AWB: {'prefix': '706', 'number': '51682923', 'origin': 'JNB', 'destination': 'LLW', 'split_code': 'S', 'pieces': '3', 'weight': '162.0', 'volume': '0.90', 'description': 'AUTOMOTIVE SPAR', 'special_codes': [], 'uld': 4}
2025-06-14 08:56:26,642 - app - INFO - Processing line: /GEN
2025-06-14 08:56:26,642 - app - INFO - Found special code line: /GEN
2025-06-14 08:56:26,642 - app - INFO - Processing line: 706-51491440DXBLLW/T1K221.0MC0.73/POWER CORE CABL
2025-06-14 08:56:26,642 - app - INFO - Found AWB line: 706-51491440DXBLLW/T1K221.0MC0.73/POWER CORE CABL
2025-06-14 08:56:26,642 - app - INFO - Adding AWB: {'prefix': '706', 'number': '51491440', 'origin': 'DXB', 'destination': 'LLW', 'split_code': 'T', 'pieces': '1', 'weight': '221.0', 'volume': '0.73', 'description': 'POWER CORE CABL', 'special_codes': [], 'uld': 4}
2025-06-14 08:56:26,642 - app - INFO - Processing line: /EAP/HEA/GEN
2025-06-14 08:56:26,642 - app - INFO - Found special code line: /EAP/HEA/GEN
2025-06-14 08:56:26,642 - app - INFO - Processing line: 706-51763994BOMLLW/P1K326.0MC1.64T2/SUPLLY OF 11KV
2025-06-14 08:56:26,642 - app - INFO - Found AWB line: 706-51763994BOMLLW/P1K326.0MC1.64T2/SUPLLY OF 11KV
2025-06-14 08:56:26,642 - app - INFO - Adding AWB: {'prefix': '706', 'number': '51763994', 'origin': 'BOM', 'destination': 'LLW', 'split_code': 'P', 'pieces': '1', 'weight': '326.0', 'volume': '1.64', 'description': 'SUPLLY OF 11KV', 'special_codes': [], 'uld': 4}
2025-06-14 08:56:26,643 - app - INFO - Processing line: /HEA/GEN
2025-06-14 08:56:26,643 - app - INFO - Found special code line: /HEA/GEN
2025-06-14 08:56:26,643 - app - INFO - Processing line: 706-51723943CDGLLW/T1K8.6MC0.05/CONSOLS
2025-06-14 08:56:26,643 - app - INFO - Found AWB line: 706-51723943CDGLLW/T1K8.6MC0.05/CONSOLS
2025-06-14 08:56:26,643 - app - INFO - Adding AWB: {'prefix': '706', 'number': '51723943', 'origin': 'CDG', 'destination': 'LLW', 'split_code': 'T', 'pieces': '1', 'weight': '8.6', 'volume': '0.05', 'description': 'CONSOLS', 'special_codes': [], 'uld': 4}
2025-06-14 08:56:26,643 - app - INFO - Processing line: /SPX
2025-06-14 08:56:26,643 - app - INFO - Found special code line: /SPX
2025-06-14 08:56:26,643 - app - INFO - Processing line: 574-34438821CDGLLW/T1K15.0MC0.09/OXYGEN GENERATO
2025-06-14 08:56:26,643 - app - INFO - Found AWB line: 574-34438821CDGLLW/T1K15.0MC0.09/OXYGEN GENERATO
2025-06-14 08:56:26,643 - app - INFO - Adding AWB: {'prefix': '574', 'number': '34438821', 'origin': 'CDG', 'destination': 'LLW', 'split_code': 'T', 'pieces': '1', 'weight': '15.0', 'volume': '0.09', 'description': 'OXYGEN GENERATO', 'special_codes': [], 'uld': 4}
2025-06-14 08:56:26,643 - app - INFO - Processing line: 485-09322294LHRLLW/T1K12.0MC0.07/CIVIL AC PARTS
2025-06-14 08:56:26,643 - app - INFO - Found AWB line: 485-09322294LHRLLW/T1K12.0MC0.07/CIVIL AC PARTS
2025-06-14 08:56:26,643 - app - INFO - Adding AWB: {'prefix': '485', 'number': '09322294', 'origin': 'LHR', 'destination': 'LLW', 'split_code': 'T', 'pieces': '1', 'weight': '12.0', 'volume': '0.07', 'description': 'CIVIL AC PARTS', 'special_codes': [], 'uld': 4}
2025-06-14 08:56:26,643 - app - INFO - Processing line: ULD/PAJ23875SA
2025-06-14 08:56:26,644 - app - INFO - Found ULD line: ULD/PAJ23875SA
2025-06-14 08:56:26,644 - app - INFO - Adding ULD: {'type': 'PAJ', 'number': '23875', 'airline': 'SA', 'awbs': [], 'origin': 'DWC'}
2025-06-14 08:56:26,644 - app - INFO - Processing line: 706-51723906LYSLLW/T7K165.4MC1.22/CONSOLIDATION A
2025-06-14 08:56:26,644 - app - INFO - Found AWB line: 706-51723906LYSLLW/T7K165.4MC1.22/CONSOLIDATION A
2025-06-14 08:56:26,644 - app - INFO - Adding AWB: {'prefix': '706', 'number': '51723906', 'origin': 'LYS', 'destination': 'LLW', 'split_code': 'T', 'pieces': '7', 'weight': '165.4', 'volume': '1.22', 'description': 'CONSOLIDATION A', 'special_codes': [], 'uld': 5}
2025-06-14 08:56:26,644 - app - INFO - Processing line: /EAP/SPX
2025-06-14 08:56:26,644 - app - INFO - Found special code line: /EAP/SPX
2025-06-14 08:56:26,644 - app - INFO - Processing line: 706-50990704AMSLLW/T1K260.0MC2.42/FILTER EQUIPMEN
2025-06-14 08:56:26,644 - app - INFO - Found AWB line: 706-50990704AMSLLW/T1K260.0MC2.42/FILTER EQUIPMEN
2025-06-14 08:56:26,644 - app - INFO - Adding AWB: {'prefix': '706', 'number': '50990704', 'origin': 'AMS', 'destination': 'LLW', 'split_code': 'T', 'pieces': '1', 'weight': '260.0', 'volume': '2.42', 'description': 'FILTER EQUIPMEN', 'special_codes': [], 'uld': 5}
2025-06-14 08:56:26,644 - app - INFO - Processing line: /EAP/SPX/HEA
2025-06-14 08:56:26,644 - app - INFO - Found special code line: /EAP/SPX/HEA
2025-06-14 08:56:26,644 - app - INFO - Processing line: 706-51722403LHRLLW/T3K273.3MC1.64/CONSOLIDATION A
2025-06-14 08:56:26,644 - app - INFO - Found AWB line: 706-51722403LHRLLW/T3K273.3MC1.64/CONSOLIDATION A
2025-06-14 08:56:26,645 - app - INFO - Adding AWB: {'prefix': '706', 'number': '51722403', 'origin': 'LHR', 'destination': 'LLW', 'split_code': 'T', 'pieces': '3', 'weight': '273.3', 'volume': '1.64', 'description': 'CONSOLIDATION A', 'special_codes': [], 'uld': 5}
2025-06-14 08:56:26,645 - app - INFO - Processing line: /EAP
2025-06-14 08:56:26,645 - app - INFO - Found special code line: /EAP
2025-06-14 08:56:26,645 - app - INFO - Processing line: 706-51722414LHRLLW/T5K450.0MC2.72/CONSOLIDATION A
2025-06-14 08:56:26,645 - app - INFO - Found AWB line: 706-51722414LHRLLW/T5K450.0MC2.72/CONSOLIDATION A
2025-06-14 08:56:26,645 - app - INFO - Adding AWB: {'prefix': '706', 'number': '51722414', 'origin': 'LHR', 'destination': 'LLW', 'split_code': 'T', 'pieces': '5', 'weight': '450.0', 'volume': '2.72', 'description': 'CONSOLIDATION A', 'special_codes': [], 'uld': 5}
2025-06-14 08:56:26,645 - app - INFO - Processing line: /EAP
2025-06-14 08:56:26,646 - app - INFO - Found special code line: /EAP
2025-06-14 08:56:26,646 - app - INFO - Processing line: ULD/PAJ33278KQ
2025-06-14 08:56:26,646 - app - INFO - Found ULD line: ULD/PAJ33278KQ
2025-06-14 08:56:26,646 - app - INFO - Adding ULD: {'type': 'PAJ', 'number': '33278', 'airline': 'KQ', 'awbs': [], 'origin': 'DWC'}
2025-06-14 08:56:26,646 - app - INFO - Processing line: 706-51484252JNBLLW/T3K2049.0MC3.61/CREMODAN SE 334
2025-06-14 08:56:26,646 - app - INFO - Found AWB line: 706-51484252JNBLLW/T3K2049.0MC3.61/CREMODAN SE 334
2025-06-14 08:56:26,646 - app - INFO - Adding AWB: {'prefix': '706', 'number': '51484252', 'origin': 'JNB', 'destination': 'LLW', 'split_code': 'T', 'pieces': '3', 'weight': '2049.0', 'volume': '3.61', 'description': 'CREMODAN SE 334', 'special_codes': [], 'uld': 6}
2025-06-14 08:56:26,646 - app - INFO - Processing line: /GEN
2025-06-14 08:56:26,646 - app - INFO - Found special code line: /GEN
2025-06-14 08:56:26,646 - app - INFO - Processing line: 706-51723276LHRLLW/T1K113.0MC0.39/GIN FLAVOUR
2025-06-14 08:56:26,646 - app - INFO - Found AWB line: 706-51723276LHRLLW/T1K113.0MC0.39/GIN FLAVOUR
2025-06-14 08:56:26,646 - app - INFO - Adding AWB: {'prefix': '706', 'number': '51723276', 'origin': 'LHR', 'destination': 'LLW', 'split_code': 'T', 'pieces': '1', 'weight': '113.0', 'volume': '0.39', 'description': 'GIN FLAVOUR', 'special_codes': [], 'uld': 6}
2025-06-14 08:56:26,647 - app - INFO - Processing line: /RFL
2025-06-14 08:56:26,647 - app - INFO - Found special code line: /RFL
2025-06-14 08:56:26,647 - app - INFO - Processing line: 706-51682923JNBLLW/S9K485.0MC2.69T12/AUTOMOTIVE SPAR
2025-06-14 08:56:26,647 - app - INFO - Found AWB line: 706-51682923JNBLLW/S9K485.0MC2.69T12/AUTOMOTIVE SPAR
2025-06-14 08:56:26,647 - app - INFO - Adding AWB: {'prefix': '706', 'number': '51682923', 'origin': 'JNB', 'destination': 'LLW', 'split_code': 'S', 'pieces': '9', 'weight': '485.0', 'volume': '2.69', 'description': 'AUTOMOTIVE SPAR', 'special_codes': [], 'uld': 6}
2025-06-14 08:56:26,647 - app - INFO - Processing line: /GEN
2025-06-14 08:56:26,647 - app - INFO - Found special code line: /GEN
2025-06-14 08:56:26,647 - app - INFO - Processing line: 706-51255282JNBLLW/T1K70.0MC0.42/LIGHTERS
2025-06-14 08:56:26,647 - app - INFO - Found AWB line: 706-51255282JNBLLW/T1K70.0MC0.42/LIGHTERS
2025-06-14 08:56:26,647 - app - INFO - Adding AWB: {'prefix': '706', 'number': '51255282', 'origin': 'JNB', 'destination': 'LLW', 'split_code': 'T', 'pieces': '1', 'weight': '70.0', 'volume': '0.42', 'description': 'LIGHTERS', 'special_codes': [], 'uld': 6}
2025-06-14 08:56:26,648 - app - INFO - Processing line: /EAP/SPX/ECC
2025-06-14 08:56:26,649 - app - INFO - Found special code line: /EAP/SPX/ECC
2025-06-14 08:56:26,650 - app - INFO - Found 36 AWBs in the input
2025-06-14 08:56:26,651 - app - INFO - Found flight line: 1/KQ2738/13JUN/NBO/5Y-KQC
2025-06-14 08:56:26,651 - app - INFO - Found arrival airport: LLW
2025-06-14 08:56:26,651 - app - INFO - Flight details: KQ2738, 13JUN, NBO, LLW, 5Y-KQC
2025-06-14 08:56:26,651 - app - INFO - Formatted departure date: 2025-06-13T00:00:00
2025-06-14 08:56:26,651 - app - INFO - Generating XML for ULD: PAJ33019KQ
2025-06-14 08:56:26,651 - app - INFO - Adding AWB to ULD 0: 706-51652742
2025-06-14 08:56:26,652 - app - INFO - Adding AWB to ULD 0: 706-51764053
2025-06-14 08:56:26,652 - app - INFO - Adding AWB to ULD 0: 706-51653630
2025-06-14 08:56:26,652 - app - INFO - Adding AWB to ULD 0: 706-51168272
2025-06-14 08:56:26,652 - app - INFO - Generating XML for ULD: PAJ33162KQ
2025-06-14 08:56:26,652 - app - INFO - Adding AWB to ULD 1: 574-34424036
2025-06-14 08:56:26,652 - app - INFO - Generating XML for ULD: PAJ33167KQ
2025-06-14 08:56:26,652 - app - INFO - Adding AWB to ULD 2: 706-51764053
2025-06-14 08:56:26,652 - app - INFO - Adding AWB to ULD 2: 706-51663463
2025-06-14 08:56:26,652 - app - INFO - Adding AWB to ULD 2: 706-51722101
2025-06-14 08:56:26,652 - app - INFO - Adding AWB to ULD 2: 706-51653630
2025-06-14 08:56:26,652 - app - INFO - Adding AWB to ULD 2: 706-51689691
2025-06-14 08:56:26,653 - app - INFO - Adding AWB to ULD 2: 485-04706332
2025-06-14 08:56:26,653 - app - INFO - Adding AWB to ULD 2: 706-51667346
2025-06-14 08:56:26,653 - app - INFO - Generating XML for ULD: PAJ33219KQ
2025-06-14 08:56:26,653 - app - INFO - Adding AWB to ULD 3: 485-09319041
2025-06-14 08:56:26,653 - app - INFO - Adding AWB to ULD 3: 706-51684006
2025-06-14 08:56:26,653 - app - INFO - Adding AWB to ULD 3: 706-51764053
2025-06-14 08:56:26,653 - app - INFO - Adding AWB to ULD 3: 706-51652742
2025-06-14 08:56:26,653 - app - INFO - Adding AWB to ULD 3: 706-51616250
2025-06-14 08:56:26,653 - app - INFO - Adding AWB to ULD 3: 574-34400612
2025-06-14 08:56:26,653 - app - INFO - Adding AWB to ULD 3: 529-40087165
2025-06-14 08:56:26,653 - app - INFO - Generating XML for ULD: PAG14855KL
2025-06-14 08:56:26,654 - app - INFO - Adding AWB to ULD 4: 706-51763961
2025-06-14 08:56:26,654 - app - INFO - Adding AWB to ULD 4: 706-51736484
2025-06-14 08:56:26,654 - app - INFO - Adding AWB to ULD 4: 706-51677161
2025-06-14 08:56:26,654 - app - INFO - Adding AWB to ULD 4: 706-51682923
2025-06-14 08:56:26,654 - app - INFO - Adding AWB to ULD 4: 706-51491440
2025-06-14 08:56:26,654 - app - INFO - Adding AWB to ULD 4: 706-51763994
2025-06-14 08:56:26,654 - app - INFO - Adding AWB to ULD 4: 706-51723943
2025-06-14 08:56:26,654 - app - INFO - Adding AWB to ULD 4: 574-34438821
2025-06-14 08:56:26,654 - app - INFO - Generating XML for ULD: PAJ23875SA
2025-06-14 08:56:26,654 - app - INFO - Adding AWB to ULD 5: 485-09322294
2025-06-14 08:56:26,654 - app - INFO - Adding AWB to ULD 5: 706-51723906
2025-06-14 08:56:26,654 - app - INFO - Adding AWB to ULD 5: 706-50990704
2025-06-14 08:56:26,655 - app - INFO - Adding AWB to ULD 5: 706-51722403
2025-06-14 08:56:26,655 - app - INFO - Generating XML for ULD: PAJ33278KQ
2025-06-14 08:56:26,655 - app - INFO - Adding AWB to ULD 6: 706-51722414
2025-06-14 08:56:26,655 - app - INFO - Adding AWB to ULD 6: 706-51484252
2025-06-14 08:56:26,655 - app - INFO - Adding AWB to ULD 6: 706-51723276
2025-06-14 08:56:26,655 - app - INFO - Adding AWB to ULD 6: 706-51682923
2025-06-14 08:56:26,655 - app - INFO - Adding AWB to ULD 6: 706-51255282
2025-06-14 08:56:26,655 - app - INFO - XML generated successfully, length: 37442
2025-06-14 08:58:00,325 - app - INFO - Received Flight Manifest XML generation request
2025-06-14 08:58:00,326 - app - INFO - Input text length: 2093
2025-06-14 08:58:00,326 - app - INFO - Generating Flight Manifest XML...
2025-06-14 08:58:00,326 - app - INFO - Processing 74 lines of input
2025-06-14 08:58:00,326 - app - INFO - Processing line: FFM/8
2025-06-14 08:58:00,326 - app - INFO - Processing line: 1/KQ2738/14JUN/NBO/5Y-KQC
2025-06-14 08:58:00,326 - app - INFO - Processing line: LLW
2025-06-14 08:58:00,326 - app - INFO - Processing line: 706-51652742NBOLLW/S2K70.0MC0.16T10/TACKING SCREWS
2025-06-14 08:58:00,326 - app - INFO - Found AWB line: 706-51652742NBOLLW/S2K70.0MC0.16T10/TACKING SCREWS
2025-06-14 08:58:00,327 - app - INFO - Adding AWB: {'prefix': '706', 'number': '51652742', 'origin': 'NBO', 'destination': 'LLW', 'split_code': 'S', 'pieces': '2', 'weight': '70.0', 'volume': '0.16', 'description': 'TACKING SCREWS', 'special_codes': [], 'uld': None}
2025-06-14 08:58:00,327 - app - INFO - Processing line: /EAP/GEN
2025-06-14 08:58:00,327 - app - INFO - Found special code line: /EAP/GEN
2025-06-14 08:58:00,327 - app - INFO - Processing line: ULD/PAJ33019KQ
2025-06-14 08:58:00,327 - app - INFO - Found ULD line: ULD/PAJ33019KQ
2025-06-14 08:58:00,327 - app - INFO - Adding ULD: {'type': 'PAJ', 'number': '33019', 'airline': 'KQ', 'awbs': [], 'origin': 'DWC'}
2025-06-14 08:58:00,327 - app - INFO - Processing line: 706-51764053BOMLLW/D33K833.0MC1.33T220/NCARB
2025-06-14 08:58:00,327 - app - INFO - Found AWB line: 706-51764053BOMLLW/D33K833.0MC1.33T220/NCARB
2025-06-14 08:58:00,327 - app - WARNING - Could not parse weight/volume from: D33K833.0MC1.33T220
2025-06-14 08:58:00,328 - app - INFO - Adding AWB: {'prefix': '706', 'number': '51764053', 'origin': 'BOM', 'destination': 'LLW', 'split_code': 'T', 'pieces': '1', 'weight': '0', 'volume': '0', 'description': 'NCARB', 'special_codes': [], 'uld': 0}
2025-06-14 08:58:00,328 - app - INFO - Processing line: /GEN
2025-06-14 08:58:00,328 - app - INFO - Found special code line: /GEN
2025-06-14 08:58:00,328 - app - INFO - Processing line: 706-51653630NBOLLW/S16K176.0MC1.40T37/NIPCAP STD AFRI
2025-06-14 08:58:00,328 - app - INFO - Found AWB line: 706-51653630NBOLLW/S16K176.0MC1.40T37/NIPCAP STD AFRI
2025-06-14 08:58:00,328 - app - INFO - Adding AWB: {'prefix': '706', 'number': '51653630', 'origin': 'NBO', 'destination': 'LLW', 'split_code': 'S', 'pieces': '16', 'weight': '176.0', 'volume': '1.40', 'description': 'NIPCAP STD AFRI', 'special_codes': [], 'uld': 0}
2025-06-14 08:58:00,328 - app - INFO - Processing line: /GEN
2025-06-14 08:58:00,328 - app - INFO - Found special code line: /GEN
2025-06-14 08:58:00,328 - app - INFO - Processing line: 706-51168272JNBLLW/T1K1350.0MC3.35/SPARES PARTS
2025-06-14 08:58:00,328 - app - INFO - Found AWB line: 706-51168272JNBLLW/T1K1350.0MC3.35/SPARES PARTS
2025-06-14 08:58:00,328 - app - INFO - Adding AWB: {'prefix': '706', 'number': '51168272', 'origin': 'JNB', 'destination': 'LLW', 'split_code': 'T', 'pieces': '1', 'weight': '1350.0', 'volume': '3.35', 'description': 'SPARES PARTS', 'special_codes': [], 'uld': 0}
2025-06-14 08:58:00,329 - app - INFO - Processing line: /GEN
2025-06-14 08:58:00,329 - app - INFO - Found special code line: /GEN
2025-06-14 08:58:00,329 - app - INFO - Processing line: 574-34424036BRULLW/T1K132.0MC0.79/PHARMA
2025-06-14 08:58:00,329 - app - INFO - Found AWB line: 574-34424036BRULLW/T1K132.0MC0.79/PHARMA
2025-06-14 08:58:00,329 - app - INFO - Adding AWB: {'prefix': '574', 'number': '34424036', 'origin': 'BRU', 'destination': 'LLW', 'split_code': 'T', 'pieces': '1', 'weight': '132.0', 'volume': '0.79', 'description': 'PHARMA', 'special_codes': [], 'uld': 0}
2025-06-14 08:58:00,330 - app - INFO - Processing line: ULD/PAJ33162KQ
2025-06-14 08:58:00,330 - app - INFO - Found ULD line: ULD/PAJ33162KQ
2025-06-14 08:58:00,330 - app - INFO - Adding ULD: {'type': 'PAJ', 'number': '33162', 'airline': 'KQ', 'awbs': [], 'origin': 'DWC'}
2025-06-14 08:58:00,330 - app - INFO - Processing line: 706-51764053BOMLLW/D140K3535.0MC5.64T220/NCARB
2025-06-14 08:58:00,330 - app - INFO - Found AWB line: 706-51764053BOMLLW/D140K3535.0MC5.64T220/NCARB
2025-06-14 08:58:00,330 - app - WARNING - Could not parse weight/volume from: D140K3535.0MC5.64T220
2025-06-14 08:58:00,331 - app - INFO - Adding AWB: {'prefix': '706', 'number': '51764053', 'origin': 'BOM', 'destination': 'LLW', 'split_code': 'T', 'pieces': '1', 'weight': '0', 'volume': '0', 'description': 'NCARB', 'special_codes': [], 'uld': 1}
2025-06-14 08:58:00,331 - app - INFO - Processing line: /GEN
2025-06-14 08:58:00,331 - app - INFO - Found special code line: /GEN
2025-06-14 08:58:00,331 - app - INFO - Processing line: ULD/PAJ33167KQ
2025-06-14 08:58:00,331 - app - INFO - Found ULD line: ULD/PAJ33167KQ
2025-06-14 08:58:00,331 - app - INFO - Adding ULD: {'type': 'PAJ', 'number': '33167', 'airline': 'KQ', 'awbs': [], 'origin': 'DWC'}
2025-06-14 08:58:00,331 - app - INFO - Processing line: 706-51663463NBOLLW/T1K26.0MC0.04/DRILLING MUD SA
2025-06-14 08:58:00,331 - app - INFO - Found AWB line: 706-51663463NBOLLW/T1K26.0MC0.04/DRILLING MUD SA
2025-06-14 08:58:00,332 - app - INFO - Adding AWB: {'prefix': '706', 'number': '51663463', 'origin': 'NBO', 'destination': 'LLW', 'split_code': 'T', 'pieces': '1', 'weight': '26.0', 'volume': '0.04', 'description': 'DRILLING MUD SA', 'special_codes': [], 'uld': 2}
2025-06-14 08:58:00,332 - app - INFO - Processing line: /GEN
2025-06-14 08:58:00,333 - app - INFO - Found special code line: /GEN
2025-06-14 08:58:00,333 - app - INFO - Processing line: 706-51722101LHRLLW/T2K36.0MC0.23/OPHTHALMOLOGY E
2025-06-14 08:58:00,333 - app - INFO - Found AWB line: 706-51722101LHRLLW/T2K36.0MC0.23/OPHTHALMOLOGY E
2025-06-14 08:58:00,333 - app - INFO - Adding AWB: {'prefix': '706', 'number': '51722101', 'origin': 'LHR', 'destination': 'LLW', 'split_code': 'T', 'pieces': '2', 'weight': '36.0', 'volume': '0.23', 'description': 'OPHTHALMOLOGY E', 'special_codes': [], 'uld': 2}
2025-06-14 08:58:00,333 - app - INFO - Processing line: 706-51653630NBOLLW/S21K234.0MC1.84T37/NIPCAP STD AFRI
2025-06-14 08:58:00,333 - app - INFO - Found AWB line: 706-51653630NBOLLW/S21K234.0MC1.84T37/NIPCAP STD AFRI
2025-06-14 08:58:00,333 - app - INFO - Adding AWB: {'prefix': '706', 'number': '51653630', 'origin': 'NBO', 'destination': 'LLW', 'split_code': 'S', 'pieces': '21', 'weight': '234.0', 'volume': '1.84', 'description': 'NIPCAP STD AFRI', 'special_codes': [], 'uld': 2}
2025-06-14 08:58:00,333 - app - INFO - Processing line: /GEN
2025-06-14 08:58:00,333 - app - INFO - Found special code line: /GEN
2025-06-14 08:58:00,334 - app - INFO - Processing line: 706-51689691FRABLZ/T1K26.0MC0.16/MED.DIAGN.PRODU
2025-06-14 08:58:00,334 - app - INFO - Found AWB line: 706-51689691FRABLZ/T1K26.0MC0.16/MED.DIAGN.PRODU
2025-06-14 08:58:00,334 - app - INFO - Adding AWB: {'prefix': '706', 'number': '51689691', 'origin': 'FRA', 'destination': 'BLZ', 'split_code': 'T', 'pieces': '1', 'weight': '26.0', 'volume': '0.16', 'description': 'MED.DIAGN.PRODU', 'special_codes': [], 'uld': 2}
2025-06-14 08:58:00,334 - app - INFO - Processing line: /SPX
2025-06-14 08:58:00,335 - app - INFO - Found special code line: /SPX
2025-06-14 08:58:00,335 - app - INFO - Processing line: 485-04706332CANLLW/P4K100.0MC0.01T25/SHOE MATEREAL
2025-06-14 08:58:00,335 - app - INFO - Found AWB line: 485-04706332CANLLW/P4K100.0MC0.01T25/SHOE MATEREAL
2025-06-14 08:58:00,336 - app - INFO - Adding AWB: {'prefix': '485', 'number': '04706332', 'origin': 'CAN', 'destination': 'LLW', 'split_code': 'P', 'pieces': '4', 'weight': '100.0', 'volume': '0.01', 'description': 'SHOE MATEREAL', 'special_codes': [], 'uld': 2}
2025-06-14 08:58:00,336 - app - INFO - Processing line: 706-51667346AMSLLW/T14K374.9MC1.54/KYOCERA MFPS.
2025-06-14 08:58:00,336 - app - INFO - Found AWB line: 706-51667346AMSLLW/T14K374.9MC1.54/KYOCERA MFPS.
2025-06-14 08:58:00,336 - app - INFO - Adding AWB: {'prefix': '706', 'number': '51667346', 'origin': 'AMS', 'destination': 'LLW', 'split_code': 'T', 'pieces': '14', 'weight': '374.9', 'volume': '1.54', 'description': 'KYOCERA MFPS.', 'special_codes': [], 'uld': 2}
2025-06-14 08:58:00,337 - app - INFO - Processing line: /SPX
2025-06-14 08:58:00,337 - app - INFO - Found special code line: /SPX
2025-06-14 08:58:00,337 - app - INFO - Processing line: 485-09319041MIALLW/T2K241.0MC1.45/MEDICAL EQUIPME
2025-06-14 08:58:00,337 - app - INFO - Found AWB line: 485-09319041MIALLW/T2K241.0MC1.45/MEDICAL EQUIPME
2025-06-14 08:58:00,337 - app - INFO - Adding AWB: {'prefix': '485', 'number': '09319041', 'origin': 'MIA', 'destination': 'LLW', 'split_code': 'T', 'pieces': '2', 'weight': '241.0', 'volume': '1.45', 'description': 'MEDICAL EQUIPME', 'special_codes': [], 'uld': 2}
2025-06-14 08:58:00,337 - app - INFO - Processing line: ULD/PAJ33219KQ
2025-06-14 08:58:00,337 - app - INFO - Found ULD line: ULD/PAJ33219KQ
2025-06-14 08:58:00,337 - app - INFO - Adding ULD: {'type': 'PAJ', 'number': '33219', 'airline': 'KQ', 'awbs': [], 'origin': 'DWC'}
2025-06-14 08:58:00,337 - app - INFO - Processing line: 706-51684006JNBLLW/T1K185.0MC0.71/STC SPARES
2025-06-14 08:58:00,337 - app - INFO - Found AWB line: 706-51684006JNBLLW/T1K185.0MC0.71/STC SPARES
2025-06-14 08:58:00,338 - app - INFO - Adding AWB: {'prefix': '706', 'number': '51684006', 'origin': 'JNB', 'destination': 'LLW', 'split_code': 'T', 'pieces': '1', 'weight': '185.0', 'volume': '0.71', 'description': 'STC SPARES', 'special_codes': [], 'uld': 3}
2025-06-14 08:58:00,338 - app - INFO - Processing line: /GEN
2025-06-14 08:58:00,338 - app - INFO - Found special code line: /GEN
2025-06-14 08:58:00,338 - app - INFO - Processing line: 706-51764053BOMLLW/D34K858.0MC1.37T220/NCARB
2025-06-14 08:58:00,338 - app - INFO - Found AWB line: 706-51764053BOMLLW/D34K858.0MC1.37T220/NCARB
2025-06-14 08:58:00,339 - app - WARNING - Could not parse weight/volume from: D34K858.0MC1.37T220
2025-06-14 08:58:00,339 - app - INFO - Adding AWB: {'prefix': '706', 'number': '51764053', 'origin': 'BOM', 'destination': 'LLW', 'split_code': 'T', 'pieces': '1', 'weight': '0', 'volume': '0', 'description': 'NCARB', 'special_codes': [], 'uld': 3}
2025-06-14 08:58:00,339 - app - INFO - Processing line: /GEN
2025-06-14 08:58:00,339 - app - INFO - Found special code line: /GEN
2025-06-14 08:58:00,339 - app - INFO - Processing line: 706-51652742NBOLLW/S8K168.0MC0.62T10/TACKING SCREWS
2025-06-14 08:58:00,339 - app - INFO - Found AWB line: 706-51652742NBOLLW/S8K168.0MC0.62T10/TACKING SCREWS
2025-06-14 08:58:00,339 - app - INFO - Adding AWB: {'prefix': '706', 'number': '51652742', 'origin': 'NBO', 'destination': 'LLW', 'split_code': 'S', 'pieces': '8', 'weight': '168.0', 'volume': '0.62', 'description': 'TACKING SCREWS', 'special_codes': [], 'uld': 3}
2025-06-14 08:58:00,339 - app - INFO - Processing line: /EAP/GEN
2025-06-14 08:58:00,339 - app - INFO - Found special code line: /EAP/GEN
2025-06-14 08:58:00,339 - app - INFO - Processing line: 706-51616250NBOLLW/P75K824.1MC3.15T85/SELF ADHESIVE T
2025-06-14 08:58:00,339 - app - INFO - Found AWB line: 706-51616250NBOLLW/P75K824.1MC3.15T85/SELF ADHESIVE T
2025-06-14 08:58:00,339 - app - INFO - Adding AWB: {'prefix': '706', 'number': '51616250', 'origin': 'NBO', 'destination': 'LLW', 'split_code': 'P', 'pieces': '75', 'weight': '824.1', 'volume': '3.15', 'description': 'SELF ADHESIVE T', 'special_codes': [], 'uld': 3}
2025-06-14 08:58:00,340 - app - INFO - Processing line: /EAP/GEN
2025-06-14 08:58:00,340 - app - INFO - Found special code line: /EAP/GEN
2025-06-14 08:58:00,340 - app - INFO - Processing line: 574-34400612ATLLLW/T1K77.0MC0.46/PA THERMAL TRAN
2025-06-14 08:58:00,340 - app - INFO - Found AWB line: 574-34400612ATLLLW/T1K77.0MC0.46/PA THERMAL TRAN
2025-06-14 08:58:00,340 - app - INFO - Adding AWB: {'prefix': '574', 'number': '34400612', 'origin': 'ATL', 'destination': 'LLW', 'split_code': 'T', 'pieces': '1', 'weight': '77.0', 'volume': '0.46', 'description': 'PA THERMAL TRAN', 'special_codes': [], 'uld': 3}
2025-06-14 08:58:00,340 - app - INFO - Processing line: 529-40087165NBOBLZ/T3K154.0MC0.23/PEDROLLO BC75 P
2025-06-14 08:58:00,340 - app - INFO - Found AWB line: 529-40087165NBOBLZ/T3K154.0MC0.23/PEDROLLO BC75 P
2025-06-14 08:58:00,340 - app - INFO - Adding AWB: {'prefix': '529', 'number': '40087165', 'origin': 'NBO', 'destination': 'BLZ', 'split_code': 'T', 'pieces': '3', 'weight': '154.0', 'volume': '0.23', 'description': 'PEDROLLO BC75 P', 'special_codes': [], 'uld': 3}
2025-06-14 08:58:00,340 - app - INFO - Processing line: /GEN
2025-06-14 08:58:00,340 - app - INFO - Found special code line: /GEN
2025-06-14 08:58:00,340 - app - INFO - Processing line: 706-51763961DELLLW/P52K723.0MC4.33T63/PHARMACEUTICALS
2025-06-14 08:58:00,340 - app - INFO - Found AWB line: 706-51763961DELLLW/P52K723.0MC4.33T63/PHARMACEUTICALS
2025-06-14 08:58:00,341 - app - INFO - Adding AWB: {'prefix': '706', 'number': '51763961', 'origin': 'DEL', 'destination': 'LLW', 'split_code': 'P', 'pieces': '52', 'weight': '723.0', 'volume': '4.33', 'description': 'PHARMACEUTICALS', 'special_codes': [], 'uld': 3}
2025-06-14 08:58:00,341 - app - INFO - Processing line: /GEN
2025-06-14 08:58:00,341 - app - INFO - Found special code line: /GEN
2025-06-14 08:58:00,341 - app - INFO - Processing line: ULD/PAG14855KL
2025-06-14 08:58:00,341 - app - INFO - Found ULD line: ULD/PAG14855KL
2025-06-14 08:58:00,341 - app - INFO - Adding ULD: {'type': 'PAG', 'number': '14855', 'airline': 'KL', 'awbs': [], 'origin': 'DWC'}
2025-06-14 08:58:00,341 - app - INFO - Processing line: 706-51736484LHRLLW/T1K100.0MC0.60/PERSONAL EFFECT
2025-06-14 08:58:00,341 - app - INFO - Found AWB line: 706-51736484LHRLLW/T1K100.0MC0.60/PERSONAL EFFECT
2025-06-14 08:58:00,341 - app - INFO - Adding AWB: {'prefix': '706', 'number': '51736484', 'origin': 'LHR', 'destination': 'LLW', 'split_code': 'T', 'pieces': '1', 'weight': '100.0', 'volume': '0.60', 'description': 'PERSONAL EFFECT', 'special_codes': [], 'uld': 4}
2025-06-14 08:58:00,341 - app - INFO - Processing line: 706-51677161LINLLW/T1K170.0MC1.21/SPARE TORO 25 F
2025-06-14 08:58:00,341 - app - INFO - Found AWB line: 706-51677161LINLLW/T1K170.0MC1.21/SPARE TORO 25 F
2025-06-14 08:58:00,341 - app - INFO - Adding AWB: {'prefix': '706', 'number': '51677161', 'origin': 'LIN', 'destination': 'LLW', 'split_code': 'T', 'pieces': '1', 'weight': '170.0', 'volume': '1.21', 'description': 'SPARE TORO 25 F', 'special_codes': [], 'uld': 4}
2025-06-14 08:58:00,341 - app - INFO - Processing line: /GEN
2025-06-14 08:58:00,342 - app - INFO - Found special code line: /GEN
2025-06-14 08:58:00,342 - app - INFO - Processing line: 706-51682923JNBLLW/S3K162.0MC0.90T12/AUTOMOTIVE SPAR
2025-06-14 08:58:00,342 - app - INFO - Found AWB line: 706-51682923JNBLLW/S3K162.0MC0.90T12/AUTOMOTIVE SPAR
2025-06-14 08:58:00,342 - app - INFO - Adding AWB: {'prefix': '706', 'number': '51682923', 'origin': 'JNB', 'destination': 'LLW', 'split_code': 'S', 'pieces': '3', 'weight': '162.0', 'volume': '0.90', 'description': 'AUTOMOTIVE SPAR', 'special_codes': [], 'uld': 4}
2025-06-14 08:58:00,342 - app - INFO - Processing line: /GEN
2025-06-14 08:58:00,342 - app - INFO - Found special code line: /GEN
2025-06-14 08:58:00,342 - app - INFO - Processing line: 706-51491440DXBLLW/T1K221.0MC0.73/POWER CORE CABL
2025-06-14 08:58:00,342 - app - INFO - Found AWB line: 706-51491440DXBLLW/T1K221.0MC0.73/POWER CORE CABL
2025-06-14 08:58:00,342 - app - INFO - Adding AWB: {'prefix': '706', 'number': '51491440', 'origin': 'DXB', 'destination': 'LLW', 'split_code': 'T', 'pieces': '1', 'weight': '221.0', 'volume': '0.73', 'description': 'POWER CORE CABL', 'special_codes': [], 'uld': 4}
2025-06-14 08:58:00,342 - app - INFO - Processing line: /EAP/HEA/GEN
2025-06-14 08:58:00,342 - app - INFO - Found special code line: /EAP/HEA/GEN
2025-06-14 08:58:00,342 - app - INFO - Processing line: 706-51763994BOMLLW/P1K326.0MC1.64T2/SUPLLY OF 11KV
2025-06-14 08:58:00,343 - app - INFO - Found AWB line: 706-51763994BOMLLW/P1K326.0MC1.64T2/SUPLLY OF 11KV
2025-06-14 08:58:00,343 - app - INFO - Adding AWB: {'prefix': '706', 'number': '51763994', 'origin': 'BOM', 'destination': 'LLW', 'split_code': 'P', 'pieces': '1', 'weight': '326.0', 'volume': '1.64', 'description': 'SUPLLY OF 11KV', 'special_codes': [], 'uld': 4}
2025-06-14 08:58:00,343 - app - INFO - Processing line: /HEA/GEN
2025-06-14 08:58:00,343 - app - INFO - Found special code line: /HEA/GEN
2025-06-14 08:58:00,343 - app - INFO - Processing line: 706-51723943CDGLLW/T1K8.6MC0.05/CONSOLS
2025-06-14 08:58:00,343 - app - INFO - Found AWB line: 706-51723943CDGLLW/T1K8.6MC0.05/CONSOLS
2025-06-14 08:58:00,343 - app - INFO - Adding AWB: {'prefix': '706', 'number': '51723943', 'origin': 'CDG', 'destination': 'LLW', 'split_code': 'T', 'pieces': '1', 'weight': '8.6', 'volume': '0.05', 'description': 'CONSOLS', 'special_codes': [], 'uld': 4}
2025-06-14 08:58:00,343 - app - INFO - Processing line: /SPX
2025-06-14 08:58:00,343 - app - INFO - Found special code line: /SPX
2025-06-14 08:58:00,343 - app - INFO - Processing line: 574-34438821CDGLLW/T1K15.0MC0.09/OXYGEN GENERATO
2025-06-14 08:58:00,343 - app - INFO - Found AWB line: 574-34438821CDGLLW/T1K15.0MC0.09/OXYGEN GENERATO
2025-06-14 08:58:00,344 - app - INFO - Adding AWB: {'prefix': '574', 'number': '34438821', 'origin': 'CDG', 'destination': 'LLW', 'split_code': 'T', 'pieces': '1', 'weight': '15.0', 'volume': '0.09', 'description': 'OXYGEN GENERATO', 'special_codes': [], 'uld': 4}
2025-06-14 08:58:00,344 - app - INFO - Processing line: 485-09322294LHRLLW/T1K12.0MC0.07/CIVIL AC PARTS
2025-06-14 08:58:00,344 - app - INFO - Found AWB line: 485-09322294LHRLLW/T1K12.0MC0.07/CIVIL AC PARTS
2025-06-14 08:58:00,344 - app - INFO - Adding AWB: {'prefix': '485', 'number': '09322294', 'origin': 'LHR', 'destination': 'LLW', 'split_code': 'T', 'pieces': '1', 'weight': '12.0', 'volume': '0.07', 'description': 'CIVIL AC PARTS', 'special_codes': [], 'uld': 4}
2025-06-14 08:58:00,344 - app - INFO - Processing line: ULD/PAJ23875SA
2025-06-14 08:58:00,344 - app - INFO - Found ULD line: ULD/PAJ23875SA
2025-06-14 08:58:00,344 - app - INFO - Adding ULD: {'type': 'PAJ', 'number': '23875', 'airline': 'SA', 'awbs': [], 'origin': 'DWC'}
2025-06-14 08:58:00,344 - app - INFO - Processing line: 706-51723906LYSLLW/T7K165.4MC1.22/CONSOLIDATION A
2025-06-14 08:58:00,344 - app - INFO - Found AWB line: 706-51723906LYSLLW/T7K165.4MC1.22/CONSOLIDATION A
2025-06-14 08:58:00,344 - app - INFO - Adding AWB: {'prefix': '706', 'number': '51723906', 'origin': 'LYS', 'destination': 'LLW', 'split_code': 'T', 'pieces': '7', 'weight': '165.4', 'volume': '1.22', 'description': 'CONSOLIDATION A', 'special_codes': [], 'uld': 5}
2025-06-14 08:58:00,344 - app - INFO - Processing line: /EAP/SPX
2025-06-14 08:58:00,344 - app - INFO - Found special code line: /EAP/SPX
2025-06-14 08:58:00,344 - app - INFO - Processing line: 706-50990704AMSLLW/T1K260.0MC2.42/FILTER EQUIPMEN
2025-06-14 08:58:00,344 - app - INFO - Found AWB line: 706-50990704AMSLLW/T1K260.0MC2.42/FILTER EQUIPMEN
2025-06-14 08:58:00,345 - app - INFO - Adding AWB: {'prefix': '706', 'number': '50990704', 'origin': 'AMS', 'destination': 'LLW', 'split_code': 'T', 'pieces': '1', 'weight': '260.0', 'volume': '2.42', 'description': 'FILTER EQUIPMEN', 'special_codes': [], 'uld': 5}
2025-06-14 08:58:00,345 - app - INFO - Processing line: /EAP/SPX/HEA
2025-06-14 08:58:00,345 - app - INFO - Found special code line: /EAP/SPX/HEA
2025-06-14 08:58:00,345 - app - INFO - Processing line: 706-51722403LHRLLW/T3K273.3MC1.64/CONSOLIDATION A
2025-06-14 08:58:00,345 - app - INFO - Found AWB line: 706-51722403LHRLLW/T3K273.3MC1.64/CONSOLIDATION A
2025-06-14 08:58:00,345 - app - INFO - Adding AWB: {'prefix': '706', 'number': '51722403', 'origin': 'LHR', 'destination': 'LLW', 'split_code': 'T', 'pieces': '3', 'weight': '273.3', 'volume': '1.64', 'description': 'CONSOLIDATION A', 'special_codes': [], 'uld': 5}
2025-06-14 08:58:00,345 - app - INFO - Processing line: /EAP
2025-06-14 08:58:00,345 - app - INFO - Found special code line: /EAP
2025-06-14 08:58:00,346 - app - INFO - Processing line: 706-51722414LHRLLW/T5K450.0MC2.72/CONSOLIDATION A
2025-06-14 08:58:00,346 - app - INFO - Found AWB line: 706-51722414LHRLLW/T5K450.0MC2.72/CONSOLIDATION A
2025-06-14 08:58:00,346 - app - INFO - Adding AWB: {'prefix': '706', 'number': '51722414', 'origin': 'LHR', 'destination': 'LLW', 'split_code': 'T', 'pieces': '5', 'weight': '450.0', 'volume': '2.72', 'description': 'CONSOLIDATION A', 'special_codes': [], 'uld': 5}
2025-06-14 08:58:00,346 - app - INFO - Processing line: /EAP
2025-06-14 08:58:00,346 - app - INFO - Found special code line: /EAP
2025-06-14 08:58:00,346 - app - INFO - Processing line: ULD/PAJ33278KQ
2025-06-14 08:58:00,346 - app - INFO - Found ULD line: ULD/PAJ33278KQ
2025-06-14 08:58:00,346 - app - INFO - Adding ULD: {'type': 'PAJ', 'number': '33278', 'airline': 'KQ', 'awbs': [], 'origin': 'DWC'}
2025-06-14 08:58:00,346 - app - INFO - Processing line: 706-51484252JNBLLW/T3K2049.0MC3.61/CREMODAN SE 334
2025-06-14 08:58:00,346 - app - INFO - Found AWB line: 706-51484252JNBLLW/T3K2049.0MC3.61/CREMODAN SE 334
2025-06-14 08:58:00,346 - app - INFO - Adding AWB: {'prefix': '706', 'number': '51484252', 'origin': 'JNB', 'destination': 'LLW', 'split_code': 'T', 'pieces': '3', 'weight': '2049.0', 'volume': '3.61', 'description': 'CREMODAN SE 334', 'special_codes': [], 'uld': 6}
2025-06-14 08:58:00,346 - app - INFO - Processing line: /GEN
2025-06-14 08:58:00,346 - app - INFO - Found special code line: /GEN
2025-06-14 08:58:00,346 - app - INFO - Processing line: 706-51723276LHRLLW/T1K113.0MC0.39/GIN FLAVOUR
2025-06-14 08:58:00,347 - app - INFO - Found AWB line: 706-51723276LHRLLW/T1K113.0MC0.39/GIN FLAVOUR
2025-06-14 08:58:00,347 - app - INFO - Adding AWB: {'prefix': '706', 'number': '51723276', 'origin': 'LHR', 'destination': 'LLW', 'split_code': 'T', 'pieces': '1', 'weight': '113.0', 'volume': '0.39', 'description': 'GIN FLAVOUR', 'special_codes': [], 'uld': 6}
2025-06-14 08:58:00,347 - app - INFO - Processing line: /RFL
2025-06-14 08:58:00,347 - app - INFO - Found special code line: /RFL
2025-06-14 08:58:00,347 - app - INFO - Processing line: 706-51682923JNBLLW/S9K485.0MC2.69T12/AUTOMOTIVE SPAR
2025-06-14 08:58:00,347 - app - INFO - Found AWB line: 706-51682923JNBLLW/S9K485.0MC2.69T12/AUTOMOTIVE SPAR
2025-06-14 08:58:00,347 - app - INFO - Adding AWB: {'prefix': '706', 'number': '51682923', 'origin': 'JNB', 'destination': 'LLW', 'split_code': 'S', 'pieces': '9', 'weight': '485.0', 'volume': '2.69', 'description': 'AUTOMOTIVE SPAR', 'special_codes': [], 'uld': 6}
2025-06-14 08:58:00,347 - app - INFO - Processing line: /GEN
2025-06-14 08:58:00,347 - app - INFO - Found special code line: /GEN
2025-06-14 08:58:00,347 - app - INFO - Processing line: 706-51255282JNBLLW/T1K70.0MC0.42/LIGHTERS
2025-06-14 08:58:00,347 - app - INFO - Found AWB line: 706-51255282JNBLLW/T1K70.0MC0.42/LIGHTERS
2025-06-14 08:58:00,348 - app - INFO - Adding AWB: {'prefix': '706', 'number': '51255282', 'origin': 'JNB', 'destination': 'LLW', 'split_code': 'T', 'pieces': '1', 'weight': '70.0', 'volume': '0.42', 'description': 'LIGHTERS', 'special_codes': [], 'uld': 6}
2025-06-14 08:58:00,348 - app - INFO - Processing line: /EAP/SPX/ECC
2025-06-14 08:58:00,348 - app - INFO - Found special code line: /EAP/SPX/ECC
2025-06-14 08:58:00,348 - app - INFO - Found 36 AWBs in the input
2025-06-14 08:58:00,348 - app - INFO - Found flight line: 1/KQ2738/14JUN/NBO/5Y-KQC
2025-06-14 08:58:00,349 - app - INFO - Found arrival airport: LLW
2025-06-14 08:58:00,349 - app - INFO - Flight details: KQ2738, 14JUN, NBO, LLW, 5Y-KQC
2025-06-14 08:58:00,349 - app - INFO - Formatted departure date: 2025-06-14T00:00:00
2025-06-14 08:58:00,349 - app - INFO - Generating XML for ULD: PAJ33019KQ
2025-06-14 08:58:00,349 - app - INFO - Adding AWB to ULD 0: 706-51652742
2025-06-14 08:58:00,349 - app - INFO - Adding AWB to ULD 0: 706-51764053
2025-06-14 08:58:00,349 - app - INFO - Adding AWB to ULD 0: 706-51653630
2025-06-14 08:58:00,349 - app - INFO - Adding AWB to ULD 0: 706-51168272
2025-06-14 08:58:00,349 - app - INFO - Generating XML for ULD: PAJ33162KQ
2025-06-14 08:58:00,349 - app - INFO - Adding AWB to ULD 1: 574-34424036
2025-06-14 08:58:00,349 - app - INFO - Generating XML for ULD: PAJ33167KQ
2025-06-14 08:58:00,349 - app - INFO - Adding AWB to ULD 2: 706-51764053
2025-06-14 08:58:00,350 - app - INFO - Adding AWB to ULD 2: 706-51663463
2025-06-14 08:58:00,350 - app - INFO - Adding AWB to ULD 2: 706-51722101
2025-06-14 08:58:00,350 - app - INFO - Adding AWB to ULD 2: 706-51653630
2025-06-14 08:58:00,350 - app - INFO - Adding AWB to ULD 2: 706-51689691
2025-06-14 08:58:00,350 - app - INFO - Adding AWB to ULD 2: 485-04706332
2025-06-14 08:58:00,350 - app - INFO - Adding AWB to ULD 2: 706-51667346
2025-06-14 08:58:00,350 - app - INFO - Generating XML for ULD: PAJ33219KQ
2025-06-14 08:58:00,350 - app - INFO - Adding AWB to ULD 3: 485-09319041
2025-06-14 08:58:00,350 - app - INFO - Adding AWB to ULD 3: 706-51684006
2025-06-14 08:58:00,350 - app - INFO - Adding AWB to ULD 3: 706-51764053
2025-06-14 08:58:00,351 - app - INFO - Adding AWB to ULD 3: 706-51652742
2025-06-14 08:58:00,351 - app - INFO - Adding AWB to ULD 3: 706-51616250
2025-06-14 08:58:00,351 - app - INFO - Adding AWB to ULD 3: 574-34400612
2025-06-14 08:58:00,351 - app - INFO - Adding AWB to ULD 3: 529-40087165
2025-06-14 08:58:00,351 - app - INFO - Generating XML for ULD: PAG14855KL
2025-06-14 08:58:00,351 - app - INFO - Adding AWB to ULD 4: 706-51763961
2025-06-14 08:58:00,351 - app - INFO - Adding AWB to ULD 4: 706-51736484
2025-06-14 08:58:00,351 - app - INFO - Adding AWB to ULD 4: 706-51677161
2025-06-14 08:58:00,351 - app - INFO - Adding AWB to ULD 4: 706-51682923
2025-06-14 08:58:00,351 - app - INFO - Adding AWB to ULD 4: 706-51491440
2025-06-14 08:58:00,351 - app - INFO - Adding AWB to ULD 4: 706-51763994
2025-06-14 08:58:00,352 - app - INFO - Adding AWB to ULD 4: 706-51723943
2025-06-14 08:58:00,352 - app - INFO - Adding AWB to ULD 4: 574-34438821
2025-06-14 08:58:00,352 - app - INFO - Generating XML for ULD: PAJ23875SA
2025-06-14 08:58:00,352 - app - INFO - Adding AWB to ULD 5: 485-09322294
2025-06-14 08:58:00,352 - app - INFO - Adding AWB to ULD 5: 706-51723906
2025-06-14 08:58:00,352 - app - INFO - Adding AWB to ULD 5: 706-50990704
2025-06-14 08:58:00,352 - app - INFO - Adding AWB to ULD 5: 706-51722403
2025-06-14 08:58:00,352 - app - INFO - Generating XML for ULD: PAJ33278KQ
2025-06-14 08:58:00,352 - app - INFO - Adding AWB to ULD 6: 706-51722414
2025-06-14 08:58:00,352 - app - INFO - Adding AWB to ULD 6: 706-51484252
2025-06-14 08:58:00,352 - app - INFO - Adding AWB to ULD 6: 706-51723276
2025-06-14 08:58:00,353 - app - INFO - Adding AWB to ULD 6: 706-51682923
2025-06-14 08:58:00,353 - app - INFO - Adding AWB to ULD 6: 706-51255282
2025-06-14 08:58:00,353 - app - INFO - XML generated successfully, length: 37442
2025-06-14 09:51:17,460 - app - INFO - Serving static file: xfwb.html
2025-06-14 09:51:17,848 - app - INFO - Serving static file: favicon.ico
2025-06-14 09:51:18,759 - app - INFO - Serving static file: xfzb.html
2025-06-14 10:05:45,220 - app - INFO - IATA XML Generator startup
2025-06-14 10:05:45,220 - app - INFO - Static folder path: /var/www/xmlmaker/frontend/static
2025-06-14 10:05:45,229 - app - INFO - Starting server on port 5002
2025-06-14 10:05:45,283 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-06-14 10:05:45,283 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-14 10:05:45,305 - werkzeug - INFO -  * Restarting with stat
2025-06-14 10:05:45,622 - app - INFO - IATA XML Generator startup
2025-06-14 10:05:45,622 - app - INFO - Static folder path: /var/www/xmlmaker/frontend/static
2025-06-14 10:05:45,643 - app - INFO - Starting server on port 5002
2025-06-14 10:05:45,681 - werkzeug - WARNING -  * Debugger is active!
2025-06-14 10:05:45,726 - werkzeug - INFO -  * Debugger PIN: 809-868-237
2025-06-14 10:06:24,694 - app - INFO - IATA XML Generator startup
2025-06-14 10:06:24,694 - app - INFO - Static folder path: /var/www/xmlmaker/frontend/static
2025-06-14 10:06:37,864 - app - INFO - IATA XML Generator startup
2025-06-14 10:06:37,865 - app - INFO - Static folder path: /var/www/xmlmaker/frontend/static
2025-06-14 10:06:37,879 - app - INFO - Starting server on port 5002
2025-06-14 10:06:37,922 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5002
 * Running on http://************:5002
2025-06-14 10:06:37,924 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-14 10:06:37,930 - werkzeug - INFO -  * Restarting with stat
2025-06-14 10:06:38,510 - app - INFO - IATA XML Generator startup
2025-06-14 10:06:38,511 - app - INFO - Static folder path: /var/www/xmlmaker/frontend/static
2025-06-14 10:06:38,523 - app - INFO - Starting server on port 5002
2025-06-14 10:06:38,546 - werkzeug - WARNING -  * Debugger is active!
2025-06-14 10:06:38,551 - werkzeug - INFO -  * Debugger PIN: 809-868-237
2025-06-14 10:06:58,404 - app - INFO - Received XFZB XML generation request
2025-06-14 10:06:58,407 - app - INFO - Input text length: 89
2025-06-14 10:06:58,409 - app - INFO - Generating XFZB XML...
2025-06-14 10:06:58,410 - xfzb_generator - INFO - Parsing input text: SHA123|706-51663054|KQ7001|2014-07-07T11:38:25|1|10|10.0|1.01|DXB-LLW|LAPTOPS BOX|ECC/EAP
2025-06-14 10:06:58,411 - xfzb_generator - INFO - Processing line 1: SHA123|706-51663054|KQ7001|2014-07-07T11:38:25|1|10|10.0|1.01|DXB-LLW|LAPTOPS BOX|ECC/EAP
2025-06-14 10:06:58,412 - xfzb_generator - INFO - Parsed HAWB: SHA123, MAWB: 706-51663054, Route: DXB-LLW
2025-06-14 10:06:58,414 - app - INFO - XFZB XML generated successfully (single file), length: 5797
2025-06-14 10:06:58,419 - werkzeug - INFO - 127.0.0.1 - - [14/Jun/2025 10:06:58] "POST /api/generate-xfzb HTTP/1.1" 200 -
2025-06-14 10:07:08,838 - app - INFO - Received XFZB XML generation request
2025-06-14 10:07:08,841 - app - INFO - Input text length: 179
2025-06-14 10:07:08,850 - app - INFO - Generating XFZB XML...
2025-06-14 10:07:08,851 - xfzb_generator - INFO - Parsing input text: SHA123|706-51663054|KQ7001|2014-07-07T11:38:25|1|10|10.0|1.01|DXB-LLW|LAPTOPS BOX|ECC/EAP
SHA124|706-51663055|KQ7002|2014-07-07T12:38:25|2|20|20.0|2.01|DXB-LLW|ELECTRONICS|ECC/EAP
2025-06-14 10:07:08,851 - xfzb_generator - INFO - Processing line 1: SHA123|706-51663054|KQ7001|2014-07-07T11:38:25|1|10|10.0|1.01|DXB-LLW|LAPTOPS BOX|ECC/EAP
2025-06-14 10:07:08,851 - xfzb_generator - INFO - Parsed HAWB: SHA123, MAWB: 706-51663054, Route: DXB-LLW
2025-06-14 10:07:08,851 - xfzb_generator - INFO - Processing line 2: SHA124|706-51663055|KQ7002|2014-07-07T12:38:25|2|20|20.0|2.01|DXB-LLW|ELECTRONICS|ECC/EAP
2025-06-14 10:07:08,851 - xfzb_generator - INFO - Parsed HAWB: SHA124, MAWB: 706-51663055, Route: DXB-LLW
2025-06-14 10:07:08,852 - app - INFO - XFZB XML generated successfully (2 files)
2025-06-14 10:07:08,858 - werkzeug - INFO - 127.0.0.1 - - [14/Jun/2025 10:07:08] "POST /api/generate-xfzb HTTP/1.1" 200 -
2025-06-14 10:07:16,826 - app - INFO - XFZB sample data requested
2025-06-14 10:07:16,827 - app - INFO - XFZB sample data sent
2025-06-14 10:07:16,827 - werkzeug - INFO - 127.0.0.1 - - [14/Jun/2025 10:07:16] "GET /api/sample-xfzb HTTP/1.1" 200 -
2025-06-14 10:07:27,660 - app - INFO - Serving static file: xfzb.html
2025-06-14 10:07:27,672 - werkzeug - INFO - 127.0.0.1 - - [14/Jun/2025 10:07:27] "[36mGET /xfzb.html HTTP/1.1[0m" 304 -
2025-06-14 10:07:28,899 - app - INFO - Serving static file: favicon.ico
2025-06-14 10:07:28,900 - werkzeug - INFO - 127.0.0.1 - - [14/Jun/2025 10:07:28] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-06-14 10:07:34,527 - app - INFO - XFZB sample data requested
2025-06-14 10:07:34,528 - app - INFO - XFZB sample data sent
2025-06-14 10:07:34,529 - werkzeug - INFO - 127.0.0.1 - - [14/Jun/2025 10:07:34] "GET /api/sample-xfzb HTTP/1.1" 200 -
2025-06-14 10:07:43,068 - app - INFO - Received XFZB XML generation request
2025-06-14 10:07:43,068 - app - INFO - Input text length: 179
2025-06-14 10:07:43,069 - app - INFO - Generating XFZB XML...
2025-06-14 10:07:43,069 - xfzb_generator - INFO - Parsing input text: SHA123|706-51663054|KQ7001|2014-07-07T11:38:25|1|10|10.0|1.01|DXB-LLW|LAPTOPS BOX|ECC/EAP
SHA124|706-51663055|KQ7002|2014-07-07T12:38:25|2|20|20.0|2.01|DXB-LLW|ELECTRONICS|ECC/EAP
2025-06-14 10:07:43,069 - xfzb_generator - INFO - Processing line 1: SHA123|706-51663054|KQ7001|2014-07-07T11:38:25|1|10|10.0|1.01|DXB-LLW|LAPTOPS BOX|ECC/EAP
2025-06-14 10:07:43,069 - xfzb_generator - INFO - Parsed HAWB: SHA123, MAWB: 706-51663054, Route: DXB-LLW
2025-06-14 10:07:43,072 - xfzb_generator - INFO - Processing line 2: SHA124|706-51663055|KQ7002|2014-07-07T12:38:25|2|20|20.0|2.01|DXB-LLW|ELECTRONICS|ECC/EAP
2025-06-14 10:07:43,074 - xfzb_generator - INFO - Parsed HAWB: SHA124, MAWB: 706-51663055, Route: DXB-LLW
2025-06-14 10:07:43,089 - app - INFO - XFZB XML generated successfully (2 files)
2025-06-14 10:07:43,093 - werkzeug - INFO - 127.0.0.1 - - [14/Jun/2025 10:07:43] "POST /api/generate-xfzb HTTP/1.1" 200 -
2025-06-14 10:07:45,756 - werkzeug - INFO -  * Detected change in '/var/www/xmlmaker/xfwb_generator.py', reloading
2025-06-14 10:07:46,057 - werkzeug - INFO -  * Restarting with stat
2025-06-14 10:07:46,630 - app - INFO - IATA XML Generator startup
2025-06-14 10:07:46,631 - app - INFO - Static folder path: /var/www/xmlmaker/frontend/static
2025-06-14 10:07:46,640 - app - INFO - Starting server on port 5002
2025-06-14 10:07:46,651 - werkzeug - WARNING -  * Debugger is active!
2025-06-14 10:07:46,669 - werkzeug - INFO -  * Debugger PIN: 809-868-237
2025-06-14 10:07:53,314 - app - INFO - Received XFZB XML generation request
2025-06-14 10:07:53,314 - app - INFO - Input text length: 179
2025-06-14 10:07:53,317 - app - INFO - Generating XFZB XML...
2025-06-14 10:07:53,317 - xfzb_generator - INFO - Parsing input text: SHA123|706-51663054|KQ7001|2014-07-07T11:38:25|1|10|10.0|1.01|DXB-LLW|LAPTOPS BOX|ECC/EAP
SHA124|706-51663055|KQ7002|2014-07-07T12:38:25|2|20|20.0|2.01|DXB-LLW|ELECTRONICS|ECC/EAP
2025-06-14 10:07:53,317 - xfzb_generator - INFO - Processing line 1: SHA123|706-51663054|KQ7001|2014-07-07T11:38:25|1|10|10.0|1.01|DXB-LLW|LAPTOPS BOX|ECC/EAP
2025-06-14 10:07:53,317 - xfzb_generator - INFO - Parsed HAWB: SHA123, MAWB: 706-51663054, Route: DXB-LLW
2025-06-14 10:07:53,318 - xfzb_generator - INFO - Processing line 2: SHA124|706-51663055|KQ7002|2014-07-07T12:38:25|2|20|20.0|2.01|DXB-LLW|ELECTRONICS|ECC/EAP
2025-06-14 10:07:53,318 - xfzb_generator - INFO - Parsed HAWB: SHA124, MAWB: 706-51663055, Route: DXB-LLW
2025-06-14 10:07:53,319 - app - INFO - XFZB XML generated successfully (2 files)
2025-06-14 10:07:53,323 - werkzeug - INFO - 127.0.0.1 - - [14/Jun/2025 10:07:53] "POST /api/generate-xfzb HTTP/1.1" 200 -
2025-06-14 10:08:08,421 - xfwb_generator - INFO - Parsing input text: 706-51663054NBOLLW/T1K138.0MC1.01/HUMAN REMAINS
2025-06-14 10:08:08,429 - xfwb_generator - INFO - Found AWB: 706-51663054
2025-06-14 10:08:08,438 - xfwb_generator - INFO - Parsed 1 AWBs
2025-06-14 10:08:49,905 - werkzeug - INFO -  * Detected change in '/var/www/xmlmaker/xfzb_generator.py', reloading
2025-06-14 10:08:50,245 - werkzeug - INFO -  * Restarting with stat
2025-06-14 10:08:51,434 - app - INFO - IATA XML Generator startup
2025-06-14 10:08:51,437 - app - INFO - Static folder path: /var/www/xmlmaker/frontend/static
2025-06-14 10:08:51,457 - app - INFO - Starting server on port 5002
2025-06-14 10:08:51,481 - werkzeug - WARNING -  * Debugger is active!
2025-06-14 10:08:51,519 - werkzeug - INFO -  * Debugger PIN: 809-868-237
2025-06-14 10:10:35,782 - werkzeug - INFO -  * Detected change in '/var/www/xmlmaker/xfzb_generator.py', reloading
2025-06-14 10:10:35,883 - werkzeug - INFO -  * Restarting with stat
2025-06-14 10:10:36,518 - app - INFO - IATA XML Generator startup
2025-06-14 10:10:36,519 - app - INFO - Static folder path: /var/www/xmlmaker/frontend/static
2025-06-14 10:10:36,530 - app - INFO - Starting server on port 5002
2025-06-14 10:10:36,537 - werkzeug - WARNING -  * Debugger is active!
2025-06-14 10:10:36,543 - werkzeug - INFO -  * Debugger PIN: 809-868-237
2025-06-14 10:10:46,567 - werkzeug - INFO -  * Detected change in '/var/www/xmlmaker/xfzb_generator.py', reloading
2025-06-14 10:10:46,781 - werkzeug - INFO -  * Restarting with stat
2025-06-14 10:10:47,080 - app - INFO - IATA XML Generator startup
2025-06-14 10:10:47,088 - app - INFO - Static folder path: /var/www/xmlmaker/frontend/static
2025-06-14 10:10:47,115 - app - INFO - Starting server on port 5002
2025-06-14 10:10:47,122 - werkzeug - WARNING -  * Debugger is active!
2025-06-14 10:10:47,134 - werkzeug - INFO -  * Debugger PIN: 809-868-237
2025-06-14 10:14:30,413 - app - INFO - Received XFZB XML generation request
2025-06-14 10:14:30,420 - app - INFO - Input text length: 89
2025-06-14 10:14:30,424 - app - INFO - Generating XFZB XML...
2025-06-14 10:14:30,424 - xfzb_generator - INFO - Parsing input text: SHA123|706-51663054|KQ7001|2014-07-07T11:38:25|1|10|10.0|1.01|DXB-LLW|LAPTOPS BOX|ECC/EAP
2025-06-14 10:14:30,428 - xfzb_generator - INFO - Processing line 1: SHA123|706-51663054|KQ7001|2014-07-07T11:38:25|1|10|10.0|1.01|DXB-LLW|LAPTOPS BOX|ECC/EAP
2025-06-14 10:14:30,428 - xfzb_generator - INFO - Parsed HAWB: SHA123, MAWB: 706-51663054, Route: DXB-LLW
2025-06-14 10:14:30,433 - app - INFO - XFZB XML generated successfully (single file), length: 5809
2025-06-14 10:14:30,440 - werkzeug - INFO - 127.0.0.1 - - [14/Jun/2025 10:14:30] "POST /api/generate-xfzb HTTP/1.1" 200 -
2025-06-14 10:14:59,791 - app - INFO - Serving static file: xfzb.html
2025-06-14 10:14:59,805 - werkzeug - INFO - 127.0.0.1 - - [14/Jun/2025 10:14:59] "GET /xfzb.html HTTP/1.1" 200 -
2025-06-14 10:15:06,343 - app - INFO - Serving static file: xfwb.html
2025-06-14 10:15:06,349 - werkzeug - INFO - 127.0.0.1 - - [14/Jun/2025 10:15:06] "GET /xfwb.html HTTP/1.1" 200 -
2025-06-14 10:16:26,656 - app - INFO - Serving static file: xfzb.html
2025-06-14 10:16:26,657 - werkzeug - INFO - 127.0.0.1 - - [14/Jun/2025 10:16:26] "[36mGET /xfzb.html HTTP/1.1[0m" 304 -
2025-06-14 10:16:34,551 - app - INFO - XFZB sample data requested
2025-06-14 10:16:34,552 - app - INFO - XFZB sample data sent
2025-06-14 10:16:34,558 - werkzeug - INFO - 127.0.0.1 - - [14/Jun/2025 10:16:34] "GET /api/sample-xfzb HTTP/1.1" 200 -
2025-06-14 10:16:37,504 - app - INFO - Received XFZB XML generation request
2025-06-14 10:16:37,504 - app - INFO - Input text length: 179
2025-06-14 10:16:37,504 - app - INFO - Generating XFZB XML...
2025-06-14 10:16:37,504 - xfzb_generator - INFO - Parsing input text: SHA123|706-51663054|KQ7001|2014-07-07T11:38:25|1|10|10.0|1.01|DXB-LLW|LAPTOPS BOX|ECC/EAP
SHA124|706-51663055|KQ7002|2014-07-07T12:38:25|2|20|20.0|2.01|DXB-LLW|ELECTRONICS|ECC/EAP
2025-06-14 10:16:37,504 - xfzb_generator - INFO - Processing line 1: SHA123|706-51663054|KQ7001|2014-07-07T11:38:25|1|10|10.0|1.01|DXB-LLW|LAPTOPS BOX|ECC/EAP
2025-06-14 10:16:37,505 - xfzb_generator - INFO - Parsed HAWB: SHA123, MAWB: 706-51663054, Route: DXB-LLW
2025-06-14 10:16:37,505 - xfzb_generator - INFO - Processing line 2: SHA124|706-51663055|KQ7002|2014-07-07T12:38:25|2|20|20.0|2.01|DXB-LLW|ELECTRONICS|ECC/EAP
2025-06-14 10:16:37,505 - xfzb_generator - INFO - Parsed HAWB: SHA124, MAWB: 706-51663055, Route: DXB-LLW
2025-06-14 10:16:37,507 - app - INFO - XFZB XML generated successfully (2 files)
2025-06-14 10:16:37,507 - werkzeug - INFO - 127.0.0.1 - - [14/Jun/2025 10:16:37] "POST /api/generate-xfzb HTTP/1.1" 200 -
2025-06-14 10:16:50,209 - app - INFO - Received XFZB XML generation request
2025-06-14 10:16:50,209 - app - INFO - Input text length: 89
2025-06-14 10:16:50,209 - app - INFO - Generating XFZB XML...
2025-06-14 10:16:50,209 - xfzb_generator - INFO - Parsing input text: SHA123|706-51663054|KQ7001|2014-07-07T11:38:25|1|10|10.0|1.01|DXB-LLW|LAPTOPS BOX|ECC/EAP
2025-06-14 10:16:50,209 - xfzb_generator - INFO - Processing line 1: SHA123|706-51663054|KQ7001|2014-07-07T11:38:25|1|10|10.0|1.01|DXB-LLW|LAPTOPS BOX|ECC/EAP
2025-06-14 10:16:50,209 - xfzb_generator - INFO - Parsed HAWB: SHA123, MAWB: 706-51663054, Route: DXB-LLW
2025-06-14 10:16:50,210 - app - INFO - XFZB XML generated successfully (single file), length: 5809
2025-06-14 10:16:50,210 - werkzeug - INFO - 127.0.0.1 - - [14/Jun/2025 10:16:50] "POST /api/generate-xfzb HTTP/1.1" 200 -
2025-06-14 10:16:59,928 - app - INFO - XFWB sample data requested
2025-06-14 10:16:59,928 - app - INFO - XFWB sample data sent
2025-06-14 10:16:59,928 - werkzeug - INFO - 127.0.0.1 - - [14/Jun/2025 10:16:59] "GET /api/sample-xfwb HTTP/1.1" 200 -
2025-06-14 10:17:02,720 - app - INFO - Received XFWB XML generation request
2025-06-14 10:17:02,729 - app - INFO - Input text length: 102
2025-06-14 10:17:02,733 - app - INFO - Generating XFWB XML...
2025-06-14 10:17:02,747 - xfwb_generator - INFO - Parsing input text: 706-51663054NBOLLW/T1K138.0MC1.01/HUMAN REMAINS
/HUM
706-60609942NBOLLW/T4K25.0MC0.01/DHL EXPRESS
/COU
2025-06-14 10:17:02,748 - xfwb_generator - INFO - Found AWB: 706-51663054
2025-06-14 10:17:02,748 - xfwb_generator - INFO - Added Handling Instructions codes to AWB 706-51663054: ['HUM']
2025-06-14 10:17:02,748 - xfwb_generator - INFO - Found AWB: 706-60609942
2025-06-14 10:17:02,749 - xfwb_generator - INFO - Added Handling Instructions codes to AWB 706-60609942: ['COU']
2025-06-14 10:17:02,749 - xfwb_generator - INFO - Parsed 2 AWBs
2025-06-14 10:17:02,750 - xfwb_generator - INFO - Generating consolidated XML for 2 AWBs
2025-06-14 10:17:02,751 - xfwb_generator - INFO - Processing AWB 1/2: 706-51663054
2025-06-14 10:17:02,752 - xfwb_generator - INFO - Processing AWB 2/2: 706-60609942
2025-06-14 10:17:02,761 - app - INFO - XFWB XML generated successfully, length: 9452
2025-06-14 10:17:02,761 - app - INFO - Multiple AWBs detected in the response
2025-06-14 10:17:02,762 - app - INFO - Found 2 AWBs in the response
2025-06-14 10:17:02,763 - werkzeug - INFO - 127.0.0.1 - - [14/Jun/2025 10:17:02] "POST /api/generate-xfwb HTTP/1.1" 200 -
2025-06-14 10:17:20,320 - app - INFO - Received XFWB XML generation request
2025-06-14 10:17:20,321 - app - INFO - Input text length: 52
2025-06-14 10:17:20,321 - app - INFO - Generating XFWB XML...
2025-06-14 10:17:20,321 - xfwb_generator - INFO - Parsing input text: 706-51663054NBOLLW/T1K138.0MC1.01/HUMAN REMAINS
/HUM
2025-06-14 10:17:20,321 - xfwb_generator - INFO - Found AWB: 706-51663054
2025-06-14 10:17:20,321 - xfwb_generator - INFO - Added Handling Instructions codes to AWB 706-51663054: ['HUM']
2025-06-14 10:17:20,322 - xfwb_generator - INFO - Parsed 1 AWBs
2025-06-14 10:17:20,322 - app - INFO - XFWB XML generated successfully, length: 4711
2025-06-14 10:17:20,322 - werkzeug - INFO - 127.0.0.1 - - [14/Jun/2025 10:17:20] "POST /api/generate-xfwb HTTP/1.1" 200 -
2025-06-14 10:17:24,736 - app - INFO - Serving static file: index.html
2025-06-14 10:17:24,737 - werkzeug - INFO - 127.0.0.1 - - [14/Jun/2025 10:17:24] "[36mGET /index.html HTTP/1.1[0m" 304 -
2025-06-14 10:21:22,548 - app - INFO - Serving static file: xfzb.html
2025-06-14 10:21:22,550 - werkzeug - INFO - 127.0.0.1 - - [14/Jun/2025 10:21:22] "GET /xfzb.html HTTP/1.1" 200 -
2025-06-14 10:21:49,613 - app - INFO - Received XFZB XML generation request
2025-06-14 10:21:49,615 - app - INFO - Input text length: 179
2025-06-14 10:21:49,615 - app - INFO - Generating XFZB XML...
2025-06-14 10:21:49,617 - xfzb_generator - INFO - Parsing input text: SHA123|706-51663054|KQ7001|2014-07-07T11:38:25|1|10|10.0|1.01|DXB-LLW|LAPTOPS BOX|ECC/EAP
SHA124|706-51663055|KQ7002|2014-07-07T12:38:25|2|20|20.0|2.01|DXB-LLW|ELECTRONICS|ECC/EAP
2025-06-14 10:21:49,629 - xfzb_generator - INFO - Processing line 1: SHA123|706-51663054|KQ7001|2014-07-07T11:38:25|1|10|10.0|1.01|DXB-LLW|LAPTOPS BOX|ECC/EAP
2025-06-14 10:21:49,631 - xfzb_generator - INFO - Parsed HAWB: SHA123, MAWB: 706-51663054, Route: DXB-LLW
2025-06-14 10:21:49,633 - xfzb_generator - INFO - Processing line 2: SHA124|706-51663055|KQ7002|2014-07-07T12:38:25|2|20|20.0|2.01|DXB-LLW|ELECTRONICS|ECC/EAP
2025-06-14 10:21:49,638 - xfzb_generator - INFO - Parsed HAWB: SHA124, MAWB: 706-51663055, Route: DXB-LLW
2025-06-14 10:21:49,650 - app - INFO - XFZB XML generated successfully (2 files)
2025-06-14 10:21:49,656 - werkzeug - INFO - 127.0.0.1 - - [14/Jun/2025 10:21:49] "POST /api/generate-xfzb HTTP/1.1" 200 -
2025-06-14 10:22:36,288 - app - INFO - Serving static file: xfwb.html
2025-06-14 10:22:43,430 - app - INFO - Serving static file: xfzb.html
2025-06-14 10:22:47,631 - app - INFO - XFZB sample data requested
2025-06-14 10:22:47,631 - app - INFO - XFZB sample data sent
2025-06-14 10:22:47,838 - app - INFO - XFZB sample data requested
2025-06-14 10:22:47,838 - app - INFO - XFZB sample data sent
2025-06-14 10:22:54,804 - app - INFO - Received XFZB XML generation request
2025-06-14 10:22:54,805 - app - INFO - Input text length: 86
2025-06-14 10:22:54,805 - app - INFO - Generating XFZB XML...
2025-06-14 10:22:54,805 - xfzb_generator - INFO - Parsing input text: SHAS51282599/T1K4.0MC0.027/panel pc
/EAW
*********/T8K180.0MC0.0/MEDICAL SUPPLIES
/DGR
2025-06-14 10:22:54,805 - xfzb_generator - ERROR - Error parsing house waybill info: Expected 11 pipe-delimited fields, got 1
Traceback (most recent call last):
  File "/var/www/xmlmaker/xfzb_generator.py", line 96, in _parse_house_waybill_info
ValueError: Expected 11 pipe-delimited fields, got 1
2025-06-14 10:22:54,806 - xfzb_generator - ERROR - Error creating XFZB XML: Expected 11 pipe-delimited fields, got 1
Traceback (most recent call last):
  File "/var/www/xmlmaker/xfzb_generator.py", line 58, in create_house_waybill_xml
  File "/var/www/xmlmaker/xfzb_generator.py", line 96, in _parse_house_waybill_info
ValueError: Expected 11 pipe-delimited fields, got 1
2025-06-14 10:22:54,807 - app - ERROR - Error in XFZB generation: Error creating XFZB XML: Expected 11 pipe-delimited fields, got 1
2025-06-14 10:23:32,684 - app - INFO - Received XFZB XML generation request
2025-06-14 10:23:32,685 - app - INFO - Input text length: 179
2025-06-14 10:23:32,685 - app - INFO - Generating XFZB XML...
2025-06-14 10:23:32,685 - xfzb_generator - INFO - Parsing input text: SHA123|706-51663054|KQ7001|2014-07-07T11:38:25|1|10|10.0|1.01|DXB-LLW|LAPTOPS BOX|ECC/EAP
SHA124|706-51663055|KQ7002|2014-07-07T12:38:25|2|20|20.0|2.01|DXB-LLW|ELECTRONICS|ECC/EAP
2025-06-14 10:23:32,685 - xfzb_generator - ERROR - Error parsing house waybill info: Expected 11 pipe-delimited fields, got 21
Traceback (most recent call last):
  File "/var/www/xmlmaker/xfzb_generator.py", line 96, in _parse_house_waybill_info
ValueError: Expected 11 pipe-delimited fields, got 21
2025-06-14 10:23:32,687 - xfzb_generator - ERROR - Error creating XFZB XML: Expected 11 pipe-delimited fields, got 21
Traceback (most recent call last):
  File "/var/www/xmlmaker/xfzb_generator.py", line 58, in create_house_waybill_xml
  File "/var/www/xmlmaker/xfzb_generator.py", line 96, in _parse_house_waybill_info
ValueError: Expected 11 pipe-delimited fields, got 21
2025-06-14 10:23:32,688 - app - ERROR - Error in XFZB generation: Error creating XFZB XML: Expected 11 pipe-delimited fields, got 21
2025-06-14 10:23:41,809 - app - INFO - Received XFZB XML generation request
2025-06-14 10:23:41,809 - app - INFO - Input text length: 89
2025-06-14 10:23:41,810 - app - INFO - Generating XFZB XML...
2025-06-14 10:23:41,810 - xfzb_generator - INFO - Parsing input text: SHA123|706-51663054|KQ7001|2014-07-07T11:38:25|1|10|10.0|1.01|DXB-LLW|LAPTOPS BOX|ECC/EAP
2025-06-14 10:23:41,810 - xfzb_generator - INFO - Parsed HAWB: SHA123, MAWB: 706-51663054, Route: DXB-LLW
2025-06-14 10:23:41,812 - app - INFO - XFZB XML generated successfully, length: 5797
2025-06-14 10:23:52,550 - app - INFO - Received XFZB XML generation request
2025-06-14 10:23:52,551 - app - INFO - Input text length: 179
2025-06-14 10:23:52,552 - app - INFO - Generating XFZB XML...
2025-06-14 10:23:52,552 - xfzb_generator - INFO - Parsing input text: SHA123|706-51663054|KQ7001|2014-07-07T11:38:25|1|10|10.0|1.01|DXB-LLW|LAPTOPS BOX|ECC/EAP
SHA124|706-51663055|KQ7002|2014-07-07T12:38:25|2|20|20.0|2.01|DXB-LLW|ELECTRONICS|ECC/EAP
2025-06-14 10:23:52,552 - xfzb_generator - ERROR - Error parsing house waybill info: Expected 11 pipe-delimited fields, got 21
Traceback (most recent call last):
  File "/var/www/xmlmaker/xfzb_generator.py", line 96, in _parse_house_waybill_info
ValueError: Expected 11 pipe-delimited fields, got 21
2025-06-14 10:23:52,552 - xfzb_generator - ERROR - Error creating XFZB XML: Expected 11 pipe-delimited fields, got 21
Traceback (most recent call last):
  File "/var/www/xmlmaker/xfzb_generator.py", line 58, in create_house_waybill_xml
  File "/var/www/xmlmaker/xfzb_generator.py", line 96, in _parse_house_waybill_info
ValueError: Expected 11 pipe-delimited fields, got 21
2025-06-14 10:23:52,553 - app - ERROR - Error in XFZB generation: Error creating XFZB XML: Expected 11 pipe-delimited fields, got 21
2025-06-14 10:27:16,077 - app - INFO - IATA XML Generator startup
2025-06-14 10:27:16,077 - app - INFO - Static folder path: /var/www/xmlmaker/frontend/static
2025-06-14 10:27:16,217 - app - INFO - IATA XML Generator startup
2025-06-14 10:27:16,217 - app - INFO - Static folder path: /var/www/xmlmaker/frontend/static
2025-06-14 10:27:16,276 - app - INFO - IATA XML Generator startup
2025-06-14 10:27:16,277 - app - INFO - Static folder path: /var/www/xmlmaker/frontend/static
2025-06-14 10:27:16,285 - app - INFO - IATA XML Generator startup
2025-06-14 10:27:16,285 - app - INFO - Static folder path: /var/www/xmlmaker/frontend/static
2025-06-14 10:27:30,211 - app - INFO - Serving static file: xfzb.html
2025-06-14 10:27:30,592 - app - INFO - Serving static file: favicon.ico
2025-06-14 10:27:52,072 - app - INFO - Received XFZB XML generation request
2025-06-14 10:27:52,072 - app - INFO - Input text length: 179
2025-06-14 10:27:52,073 - app - INFO - Generating XFZB XML...
2025-06-14 10:27:52,073 - xfzb_generator - INFO - Parsing input text: SHA123|706-51663054|KQ7001|2014-07-07T11:38:25|1|10|10.0|1.01|DXB-LLW|LAPTOPS BOX|ECC/EAP
SHA124|706-51663055|KQ7002|2014-07-07T12:38:25|2|20|20.0|2.01|DXB-LLW|ELECTRONICS|ECC/EAP
2025-06-14 10:27:52,073 - xfzb_generator - INFO - Processing line 1: SHA123|706-51663054|KQ7001|2014-07-07T11:38:25|1|10|10.0|1.01|DXB-LLW|LAPTOPS BOX|ECC/EAP
2025-06-14 10:27:52,074 - xfzb_generator - INFO - Parsed HAWB: SHA123, MAWB: 706-51663054, Route: DXB-LLW
2025-06-14 10:27:52,074 - xfzb_generator - INFO - Processing line 2: SHA124|706-51663055|KQ7002|2014-07-07T12:38:25|2|20|20.0|2.01|DXB-LLW|ELECTRONICS|ECC/EAP
2025-06-14 10:27:52,075 - xfzb_generator - INFO - Parsed HAWB: SHA124, MAWB: 706-51663055, Route: DXB-LLW
2025-06-14 10:27:52,077 - app - INFO - XFZB XML generated successfully (2 files)
2025-06-14 10:36:57,383 - app - INFO - Serving static file: xfzb.html
2025-06-14 10:36:57,803 - app - INFO - Serving static file: favicon.ico
2025-06-14 10:37:11,146 - app - INFO - Serving static file: xfwb.html
2025-06-14 10:38:47,639 - app - INFO - Serving static file: xfwb.html
2025-06-14 10:38:51,360 - app - INFO - Serving static file: xfzb.html
2025-06-14 10:39:43,762 - app - INFO - Serving static file: xfwb.html
2025-06-14 10:39:47,127 - app - INFO - XFWB sample data requested
2025-06-14 10:39:47,127 - app - INFO - XFWB sample data sent
2025-06-14 10:39:50,308 - app - INFO - Received XFWB XML generation request
2025-06-14 10:39:50,309 - app - INFO - Input text length: 102
2025-06-14 10:39:50,310 - app - INFO - Generating XFWB XML...
2025-06-14 10:39:50,310 - xfwb_generator - INFO - Parsing input text: 706-51663054NBOLLW/T1K138.0MC1.01/HUMAN REMAINS
/HUM
706-60609942NBOLLW/T4K25.0MC0.01/DHL EXPRESS
/COU
2025-06-14 10:39:50,311 - xfwb_generator - INFO - Found AWB: 706-51663054
2025-06-14 10:39:50,311 - xfwb_generator - INFO - Added Handling Instructions codes to AWB 706-51663054: ['HUM']
2025-06-14 10:39:50,311 - xfwb_generator - INFO - Found AWB: 706-60609942
2025-06-14 10:39:50,311 - xfwb_generator - INFO - Added Handling Instructions codes to AWB 706-60609942: ['COU']
2025-06-14 10:39:50,312 - xfwb_generator - INFO - Parsed 2 AWBs
2025-06-14 10:39:50,312 - xfwb_generator - INFO - Generating consolidated XML for 2 AWBs
2025-06-14 10:39:50,312 - xfwb_generator - INFO - Processing AWB 1/2: 706-51663054
2025-06-14 10:39:50,314 - xfwb_generator - INFO - Processing AWB 2/2: 706-60609942
2025-06-14 10:39:50,315 - app - INFO - XFWB XML generated successfully, length: 9452
2025-06-14 10:39:50,315 - app - INFO - Multiple AWBs detected in the response
2025-06-14 10:39:50,316 - app - INFO - Found 2 AWBs in the response
2025-06-14 10:40:00,868 - app - INFO - Received XFWB XML generation request
2025-06-14 10:40:00,869 - app - INFO - Input text length: 52
2025-06-14 10:40:00,869 - app - INFO - Generating XFWB XML...
2025-06-14 10:40:00,869 - xfwb_generator - INFO - Parsing input text: 706-51663054NBOLLW/T1K138.0MC1.01/HUMAN REMAINS
/HUM
2025-06-14 10:40:00,869 - xfwb_generator - INFO - Found AWB: 706-51663054
2025-06-14 10:40:00,870 - xfwb_generator - INFO - Added Handling Instructions codes to AWB 706-51663054: ['HUM']
2025-06-14 10:40:00,871 - xfwb_generator - INFO - Parsed 1 AWBs
2025-06-14 10:40:00,871 - app - INFO - XFWB XML generated successfully, length: 4711
2025-06-14 11:51:28,533 - app - INFO - Received XFZB XML generation request
2025-06-14 11:51:28,534 - app - INFO - Input text length: 179
2025-06-14 11:51:28,534 - app - INFO - Generating XFZB XML...
2025-06-14 11:51:28,535 - xfzb_generator - INFO - Parsing input text: SHA123|706-51663054|KQ7001|2014-07-07T11:38:25|1|10|10.0|1.01|DXB-LLW|LAPTOPS BOX|ECC/EAP
SHA124|706-51663055|KQ7002|2014-07-07T12:38:25|2|20|20.0|2.01|DXB-LLW|ELECTRONICS|ECC/EAP
2025-06-14 11:51:28,535 - xfzb_generator - INFO - Processing line 1: SHA123|706-51663054|KQ7001|2014-07-07T11:38:25|1|10|10.0|1.01|DXB-LLW|LAPTOPS BOX|ECC/EAP
2025-06-14 11:51:28,535 - xfzb_generator - INFO - Parsed HAWB: SHA123, MAWB: 706-51663054, Route: DXB-LLW
2025-06-14 11:51:28,535 - xfzb_generator - INFO - Processing line 2: SHA124|706-51663055|KQ7002|2014-07-07T12:38:25|2|20|20.0|2.01|DXB-LLW|ELECTRONICS|ECC/EAP
2025-06-14 11:51:28,537 - xfzb_generator - INFO - Parsed HAWB: SHA124, MAWB: 706-51663055, Route: DXB-LLW
2025-06-14 11:51:28,538 - app - INFO - XFZB XML generated successfully (2 files)
2025-06-14 11:53:32,924 - app - INFO - Received XFWB XML generation request
2025-06-14 11:53:32,926 - app - INFO - Input text length: 226
2025-06-14 11:53:32,927 - app - INFO - Generating XFWB XML...
2025-06-14 11:53:32,927 - xfwb_generator - INFO - Parsing input text: 706-04706332,CANLLW/,T4K100.0MC0.01/SHOE MATEREAL
706-51616250,NBOLLW/,T75K824.1MC3.15/SELF ADHESIVE T/EAP/GEN
706-51763961,DELLLW/,T52K723.0MC4.33/PHARMACEUTICALS/GEN
706-51763994,BOMLLW/,T1K326.0MC1.64/SUPPLY OF 11KV/HEA/GEN
2025-06-14 11:53:32,928 - xfwb_generator - WARNING - No valid AWBs found in the input text
2025-06-14 11:53:32,928 - xfwb_generator - WARNING - No AWBs found to generate XML
2025-06-14 11:53:32,928 - app - ERROR - Error in XFWB generation: No valid AWBs found to generate XML
2025-06-14 11:53:47,489 - app - INFO - Received XFWB XML generation request
2025-06-14 11:53:47,489 - app - INFO - Input text length: 229
2025-06-14 11:53:47,489 - app - INFO - Generating XFWB XML...
2025-06-14 11:53:47,490 - xfwb_generator - INFO - Parsing input text: 706-04706332,CANLLW/,T4K100.0MC0.01/SHOE MATEREAL
706-51616250,NBOLLW/,T75K824.1MC3.15/SELF ADHESIVE T
/EAP/GEN
706-51763961,DELLLW/,T52K723.0MC4.33/PHARMACEUTICALS
/GEN
706-51763994,BOMLLW/,T1K326.0MC1.64/SUPPLY OF 11KV
/HEA/GEN
2025-06-14 11:53:47,490 - xfwb_generator - WARNING - No valid AWBs found in the input text
2025-06-14 11:53:47,490 - xfwb_generator - WARNING - No AWBs found to generate XML
2025-06-14 11:53:47,490 - app - ERROR - Error in XFWB generation: No valid AWBs found to generate XML
2025-06-14 12:12:16,185 - app - INFO - Received XFWB XML generation request
2025-06-14 12:12:16,186 - app - INFO - Input text length: 1646
2025-06-14 12:12:16,186 - app - INFO - Generating XFWB XML...
2025-06-14 12:12:16,186 - xfwb_generator - INFO - Parsing input text: 706-04706332CANLLW/T25K625.0MC0.0625/SHOE MATEREAL
706-09319041MIALLW/T2K241.0MC1.45/MEDICAL EQUIPME
706-09322294LHRLLW/T1K12.0MC0.07/CIVIL AC PARTS/GEN
706-34400612ATLLLW/T1K77.0MC0.46/PA THERMAL TRAN
706-34424036BRULLW/T1K132.0MC0.79/PHARMA
706-34438821CDGLLW/T1K15.0MC0.09/OXYGEN GENERATOR/GEN
706-40087165NBOBLZ/T3K154.0MC0.23/PEDROLLO BC75 P/GEN
706-50990704AMSLLW/T1K260.0MC2.42/FILTER EQUIPMEN/EAP/SPX/HEA
706-51168272JNBLLW/T1K1350.0MC3.35/SPARES PARTS/GEN
706-51255282JNBLLW/T1K70.0MC0.42/LIGHTERS/EAP/SPX/ECC
706-51484252JNBLLW/T3K2049.0MC3.61/CREMODAN SE 334/GEN
706-51491440DXBLLW/T1K221.0MC0.73/POWER CORE CABL/EAP/HEA/GEN
706-51616250NBOLLW/T85K933.5MC3.57/SELF ADHESIVE T/EAP/GEN
706-51652742NBOLLW/T10K238.0MC0.78/TACKING SCREWS/EAP/GEN
706-51653630NBOLLW/T37K410.0MC3.24/NIPCAP STD AFRI/GEN
706-51663463NBOLLW/T1K26.0MC0.04/DRILLING MUD SA/GEN
706-51667346AMSLLW/T14K374.9MC1.54/KYOCERA MFPS./SPX
706-51677161LINLLW/T1K170.0MC1.21/SPARE TORO 25 F/GEN
706-51682923JNBLLW/T12K647.0MC3.59/AUTOMOTIVE SPAR/GEN
706-51684006JNBLLW/T1K185.0MC0.71/STC SPARES/GEN
706-51689691FRABLZ/T1K26.0MC0.16/MED.DIAGN.PRODU/SPX
706-51722101LHRLLW/T2K36.0MC0.23/OPHTHALMOLOGY E
706-51722403LHRLLW/T3K273.3MC1.64/CONSOLIDATION A/EAP
706-51722414LHRLLW/T5K450.0MC2.72/CONSOLIDATION A/EAP
706-51723276LHRLLW/T1K113.0MC0.39/GIN FLAVOUR/RFL
706-51723906LYSLLW/T7K165.4MC1.22/CONSOLIDATION A/EAP/SPX
706-51723943CDGLLW/T1K8.6MC0.05/CONSOLS/SPX
706-51736484LHRLLW/T1K100.0MC0.60/PERSONAL EFFECT/GEN
706-51763961DELLLW/T63K875.4MC5.25/PHARMACEUTICALS/GEN
706-51763994BOMLLW/T2K652.0MC3.28/SUPPLY OF 11KV/HEA/GEN
706-51764053BOMLLW/T220K5226.0MC8.34/NCARB/GEN
2025-06-14 12:12:16,186 - xfwb_generator - INFO - Found AWB: 706-04706332
2025-06-14 12:12:16,186 - xfwb_generator - INFO - Found AWB: 706-09319041
2025-06-14 12:12:16,186 - xfwb_generator - INFO - Found AWB: 706-09322294
2025-06-14 12:12:16,186 - xfwb_generator - INFO - Found AWB: 706-34400612
2025-06-14 12:12:16,186 - xfwb_generator - INFO - Found AWB: 706-34424036
2025-06-14 12:12:16,186 - xfwb_generator - INFO - Found AWB: 706-34438821
2025-06-14 12:12:16,186 - xfwb_generator - INFO - Found AWB: 706-40087165
2025-06-14 12:12:16,186 - xfwb_generator - INFO - Found AWB: 706-50990704
2025-06-14 12:12:16,186 - xfwb_generator - INFO - Found AWB: 706-51168272
2025-06-14 12:12:16,186 - xfwb_generator - INFO - Found AWB: 706-51255282
2025-06-14 12:12:16,186 - xfwb_generator - INFO - Found AWB: 706-51484252
2025-06-14 12:12:16,187 - xfwb_generator - INFO - Found AWB: 706-51491440
2025-06-14 12:12:16,187 - xfwb_generator - INFO - Found AWB: 706-51616250
2025-06-14 12:12:16,187 - xfwb_generator - INFO - Found AWB: 706-51652742
2025-06-14 12:12:16,187 - xfwb_generator - INFO - Found AWB: 706-51653630
2025-06-14 12:12:16,187 - xfwb_generator - INFO - Found AWB: 706-51663463
2025-06-14 12:12:16,187 - xfwb_generator - INFO - Found AWB: 706-51667346
2025-06-14 12:12:16,187 - xfwb_generator - INFO - Found AWB: 706-51677161
2025-06-14 12:12:16,187 - xfwb_generator - INFO - Found AWB: 706-51682923
2025-06-14 12:12:16,187 - xfwb_generator - INFO - Found AWB: 706-51684006
2025-06-14 12:12:16,187 - xfwb_generator - INFO - Found AWB: 706-51689691
2025-06-14 12:12:16,187 - xfwb_generator - INFO - Found AWB: 706-51722101
2025-06-14 12:12:16,187 - xfwb_generator - INFO - Found AWB: 706-51722403
2025-06-14 12:12:16,187 - xfwb_generator - INFO - Found AWB: 706-51722414
2025-06-14 12:12:16,187 - xfwb_generator - INFO - Found AWB: 706-51723276
2025-06-14 12:12:16,187 - xfwb_generator - INFO - Found AWB: 706-51723906
2025-06-14 12:12:16,187 - xfwb_generator - INFO - Found AWB: 706-51723943
2025-06-14 12:12:16,187 - xfwb_generator - INFO - Found AWB: 706-51736484
2025-06-14 12:12:16,187 - xfwb_generator - INFO - Found AWB: 706-51763961
2025-06-14 12:12:16,187 - xfwb_generator - INFO - Found AWB: 706-51763994
2025-06-14 12:12:16,187 - xfwb_generator - INFO - Found AWB: 706-51764053
2025-06-14 12:12:16,187 - xfwb_generator - INFO - Parsed 31 AWBs
2025-06-14 12:12:16,187 - xfwb_generator - INFO - Generating consolidated XML for 31 AWBs
2025-06-14 12:12:16,187 - xfwb_generator - INFO - Processing AWB 1/31: 706-04706332
2025-06-14 12:12:16,189 - xfwb_generator - INFO - Processing AWB 2/31: 706-09319041
2025-06-14 12:12:16,190 - xfwb_generator - INFO - Processing AWB 3/31: 706-09322294
2025-06-14 12:12:16,190 - xfwb_generator - INFO - Processing AWB 4/31: 706-34400612
2025-06-14 12:12:16,191 - xfwb_generator - INFO - Processing AWB 5/31: 706-34424036
2025-06-14 12:12:16,192 - xfwb_generator - INFO - Processing AWB 6/31: 706-34438821
2025-06-14 12:12:16,192 - xfwb_generator - INFO - Processing AWB 7/31: 706-40087165
2025-06-14 12:12:16,193 - xfwb_generator - INFO - Processing AWB 8/31: 706-50990704
2025-06-14 12:12:16,193 - xfwb_generator - INFO - Processing AWB 9/31: 706-51168272
2025-06-14 12:12:16,194 - xfwb_generator - INFO - Processing AWB 10/31: 706-51255282
2025-06-14 12:12:16,194 - xfwb_generator - INFO - Processing AWB 11/31: 706-51484252
2025-06-14 12:12:16,195 - xfwb_generator - INFO - Processing AWB 12/31: 706-51491440
2025-06-14 12:12:16,195 - xfwb_generator - INFO - Processing AWB 13/31: 706-51616250
2025-06-14 12:12:16,196 - xfwb_generator - INFO - Processing AWB 14/31: 706-51652742
2025-06-14 12:12:16,196 - xfwb_generator - INFO - Processing AWB 15/31: 706-51653630
2025-06-14 12:12:16,197 - xfwb_generator - INFO - Processing AWB 16/31: 706-51663463
2025-06-14 12:12:16,197 - xfwb_generator - INFO - Processing AWB 17/31: 706-51667346
2025-06-14 12:12:16,198 - xfwb_generator - INFO - Processing AWB 18/31: 706-51677161
2025-06-14 12:12:16,198 - xfwb_generator - INFO - Processing AWB 19/31: 706-51682923
2025-06-14 12:12:16,199 - xfwb_generator - INFO - Processing AWB 20/31: 706-51684006
2025-06-14 12:12:16,199 - xfwb_generator - INFO - Processing AWB 21/31: 706-51689691
2025-06-14 12:12:16,200 - xfwb_generator - INFO - Processing AWB 22/31: 706-51722101
2025-06-14 12:12:16,200 - xfwb_generator - INFO - Processing AWB 23/31: 706-51722403
2025-06-14 12:12:16,201 - xfwb_generator - INFO - Processing AWB 24/31: 706-51722414
2025-06-14 12:12:16,201 - xfwb_generator - INFO - Processing AWB 25/31: 706-51723276
2025-06-14 12:12:16,202 - xfwb_generator - INFO - Processing AWB 26/31: 706-51723906
2025-06-14 12:12:16,202 - xfwb_generator - INFO - Processing AWB 27/31: 706-51723943
2025-06-14 12:12:16,203 - xfwb_generator - INFO - Processing AWB 28/31: 706-51736484
2025-06-14 12:12:16,203 - xfwb_generator - INFO - Processing AWB 29/31: 706-51763961
2025-06-14 12:12:16,204 - xfwb_generator - INFO - Processing AWB 30/31: 706-51763994
2025-06-14 12:12:16,204 - xfwb_generator - INFO - Processing AWB 31/31: 706-51764053
2025-06-14 12:12:16,205 - app - INFO - XFWB XML generated successfully, length: 141353
2025-06-14 12:12:16,205 - app - INFO - Multiple AWBs detected in the response
2025-06-14 12:12:16,205 - app - INFO - Found 31 AWBs in the response
2025-06-14 14:26:00,288 - app - INFO - Serving static file: xfzb.html
