#!/usr/bin/env python3
"""
XFWB (Master Air Waybill) XML generator.
This module generates XFWB XML documents from simplified input format.
"""

import xml.etree.ElementTree as ET
from xml.dom import minidom
import re
import datetime
import logging
import os
import uuid

# Configure logging
log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'logs')
if not os.path.exists(log_dir):
    os.makedirs(log_dir)
log_file = os.path.join(log_dir, 'xfwb_generator.log')

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger('xfwb_generator')

class XFWBGenerator:
    def __init__(self):
        # Define XML namespaces
        self.waybill_ns = "iata:waybill:1"
        self.ram = "iata:datamodel:3"

        # Register namespaces for proper output formatting
        ET.register_namespace('ram', self.ram)

    def create_waybill_xml(self, input_text, config=None):
        """Parse input text and create XFWB XML document"""
        try:
            # Parse the waybill information from input text
            waybill_info = self._parse_waybill_info(input_text)

            # Check if we have any AWBs
            if not waybill_info.get('awbs'):
                logger.warning("No AWBs found to generate XML")
                return "<!-- No valid AWBs found to generate XML -->"

            # If we have multiple AWBs, generate a consolidated XML document
            if len(waybill_info['awbs']) > 1:
                return self._create_consolidated_xml(waybill_info, config)

            # Apply any configuration overrides for single AWB
            if config:
                waybill_info.update(config)

            # Create root element with namespaces
            root = ET.Element("ns0:Waybill")
            # Add namespace attributes (only once)
            root.set("xmlns:ns0", self.waybill_ns)
            root.set("xmlns:ram", self.ram)

            # Add header documents
            self._add_message_header(root, waybill_info)
            self._add_business_header(root, waybill_info)

            # Add master consignment
            self._add_master_consignment(root, waybill_info)

            # Return formatted XML
            return self._format_xml(root)
        except Exception as e:
            logger.error(f"Error creating XFWB XML: {str(e)}", exc_info=True)
            return f"<!-- Error creating XFWB XML: {str(e)} -->"

    def _create_consolidated_xml(self, waybill_info, config=None):
        """Create a consolidated XML document for multiple AWBs"""
        try:
            logger.info(f"Generating consolidated XML for {len(waybill_info['awbs'])} AWBs")

            # Create a container for all AWB XMLs
            consolidated_xml = '<?xml version="1.0" encoding="UTF-8"?>\n'
            consolidated_xml += '<AWBCollection>\n'

            # Process each AWB
            for i, awb in enumerate(waybill_info['awbs']):
                logger.info(f"Processing AWB {i+1}/{len(waybill_info['awbs'])}: {awb['awb_number']}")

                # Create a copy of the waybill info for this AWB
                single_awb_info = waybill_info.copy()

                # Update with this specific AWB's data
                for key, value in awb.items():
                    single_awb_info[key] = value

                # Apply any configuration overrides
                if config:
                    # Only apply non-AWB specific config
                    for key, value in config.items():
                        if key not in awb:
                            single_awb_info[key] = value

                # Create root element with namespaces
                root = ET.Element("ns0:Waybill")
                # Add namespace attributes (only once)
                root.set("xmlns:ns0", self.waybill_ns)
                root.set("xmlns:ram", self.ram)

                # Add header documents
                self._add_message_header(root, single_awb_info)
                self._add_business_header(root, single_awb_info)

                # Add master consignment
                self._add_master_consignment(root, single_awb_info)

                # Format the XML for this AWB
                awb_xml = self._format_xml(root)

                # Remove XML declaration from individual AWBs (except the first one)
                if i > 0:
                    awb_xml = awb_xml.replace('<?xml version="1.0" encoding="UTF-8"?>\n', '')

                # Add to consolidated XML
                consolidated_xml += awb_xml + '\n'

            consolidated_xml += '</AWBCollection>'
            return consolidated_xml

        except Exception as e:
            logger.error(f"Error creating consolidated XML: {str(e)}", exc_info=True)
            return f"<!-- Error creating consolidated XML: {str(e)} -->"

    def _parse_waybill_info(self, input_text):
        """Parse the input text to extract waybill information"""
        logger.info(f"Parsing input text: {input_text}")

        # Initialize dictionary to store waybill information with default values
        waybill_info = {
            'awbs': [],  # List to store multiple AWBs
            'issue_date': datetime.datetime.now().strftime('%Y-%m-%dT%H:%M:%SZ'),
            'conversation_id': str(uuid.uuid4())
        }

        # Parse the input text
        # New format: 706-50990704/740/KQ777/14JUN/AMSLLW/T1K260.0MC2.42/FILTER EQUIPMEN/EAP/SPX/HEA
        # Guide: {MAWB#}/{TYPECODE}/{CARRIER}{FLIGHT#}/{FLIGHTDATE}/{SOURCEID}{DESTINATIONID}/T{PIECE QTY}K{WEIGHT}MC{VOLUME}/{DESCRIPTION}/{SPH}

        try:
            # Split by lines in case multiple entries are provided
            lines = input_text.strip().split('\n')

            # Pattern for new AWB format with type code
            awb_pattern = r'(\d{3}-\d+)\/(\d{3})\/([A-Z]{2})(\d+)\/(\d{2}[A-Z]{3})\/([A-Z]{3})([A-Z]{3})\/T(\d+)K(\d+\.\d+)MC(\d+\.\d+)\/([^\/]+)\/?(.*)'

            # Process all lines to find AWBs
            for line in lines:
                line = line.strip()

                # Skip empty lines
                if not line:
                    continue

                # Check if this is an AWB line
                match = re.match(awb_pattern, line)
                if match:
                    # Extract components
                    mawb_number = match.group(1)
                    type_code = match.group(2)
                    carrier = match.group(3)
                    flight_number = match.group(4)
                    flight_date = match.group(5)
                    source_id = match.group(6)
                    destination_id = match.group(7)
                    piece_qty = int(match.group(8))
                    weight = float(match.group(9))
                    volume = float(match.group(10))
                    description = match.group(11).strip()
                    sph_codes = match.group(12).strip() if match.group(12) else ""

                    # Parse flight date (14JUN -> 2025-06-14T00:00:00Z)
                    flight_datetime = self._parse_flight_date(flight_date)

                    # Parse SPH codes
                    special_handling_codes = []
                    if sph_codes:
                        special_handling_codes = [code.strip() for code in sph_codes.split('/') if code.strip()]

                    current_awb = {
                        'mawb_number': mawb_number,
                        'carrier': carrier,
                        'flight_number': flight_number,
                        'flight_date': flight_date,
                        'flight_datetime': flight_datetime,
                        'source_id': source_id,
                        'destination_id': destination_id,
                        'piece_qty': piece_qty,
                        'weight': weight,
                        'volume': volume,
                        'description': description,
                        'special_handling_codes': special_handling_codes,
                        'type_code': type_code
                    }

                    waybill_info['awbs'].append(current_awb)
                    logger.info(f"Found AWB: {current_awb['mawb_number']}")

            # If no AWBs were found, log a warning
            if not waybill_info['awbs']:
                logger.warning("No valid AWBs found in the input text")
                return waybill_info

            # Set the primary AWB (first one) as the main waybill info for backward compatibility
            primary_awb = waybill_info['awbs'][0]
            for key, value in primary_awb.items():
                waybill_info[key] = value

            logger.info(f"Parsed {len(waybill_info['awbs'])} AWBs")
            return waybill_info

        except Exception as e:
            logger.error(f"Error parsing waybill info: {str(e)}", exc_info=True)
            raise

    def _parse_flight_date(self, flight_date):
        """Parse flight date from format 14JUN to YYYY-MM-DDTHH:MM:SSZ"""
        try:
            # Map month abbreviations to numbers
            month_map = {
                'JAN': '01', 'FEB': '02', 'MAR': '03', 'APR': '04',
                'MAY': '05', 'JUN': '06', 'JUL': '07', 'AUG': '08',
                'SEP': '09', 'OCT': '10', 'NOV': '11', 'DEC': '12'
            }

            # Extract day and month
            day = flight_date[:2]
            month_abbr = flight_date[2:]
            month = month_map.get(month_abbr, '01')

            # Use current year (could be made configurable)
            year = str(datetime.datetime.now().year)

            return f"{year}-{month}-{day}T00:00:00Z"
        except Exception as e:
            logger.error(f"Error parsing flight date {flight_date}: {str(e)}")
            return datetime.datetime.now().strftime('%Y-%m-%dT%H:%M:%SZ')

    def _add_message_header(self, root, waybill_info):
        """Add MessageHeaderDocument to the XML"""
        header = ET.SubElement(root, "ns0:MessageHeaderDocument")

        # Add MAWB number as ID
        ET.SubElement(header, f"{{{self.ram}}}ID").text = waybill_info.get('mawb_number', '')

        # Add document name
        ET.SubElement(header, f"{{{self.ram}}}Name").text = "Transport Loading Report"

        # Add type code with attributes
        type_code_elem = ET.SubElement(header, f"{{{self.ram}}}TypeCode")
        type_code_elem.set("listID", "1001")
        type_code_elem.set("listAgencyID", "6")
        type_code_elem.set("listVersionID", "D09A")
        type_code_elem.text = waybill_info.get('type_code', '740')

        # Add issue date/time
        ET.SubElement(header, f"{{{self.ram}}}IssueDateTime").text = waybill_info.get('issue_date', '')

        # Add purpose code
        ET.SubElement(header, f"{{{self.ram}}}PurposeCode").text = "Creation"

        # Add version ID
        ET.SubElement(header, f"{{{self.ram}}}VersionID").text = "3.00"

        # Add conversation ID
        ET.SubElement(header, f"{{{self.ram}}}ConversationID").text = waybill_info.get('conversation_id', '')

        # Add sender party
        sender = ET.SubElement(header, f"{{{self.ram}}}SenderParty")
        ET.SubElement(sender, f"{{{self.ram}}}PrimaryID", schemeID="C").text = "SENDUMSIGN"

        # Add recipient party
        recipient = ET.SubElement(header, f"{{{self.ram}}}RecipientParty")
        ET.SubElement(recipient, f"{{{self.ram}}}PrimaryID", schemeID="C").text = "RECDUMSIGN"

    def _add_business_header(self, root, waybill_info):
        """Add BusinessHeaderDocument to the XML"""
        header = ET.SubElement(root, "ns0:BusinessHeaderDocument")

        # Add MAWB number as ID
        ET.SubElement(header, f"{{{self.ram}}}ID").text = waybill_info.get('mawb_number', '')

        # Add included header note based on type code
        note = ET.SubElement(header, f"{{{self.ram}}}IncludedHeaderNote")
        type_code = waybill_info.get('type_code', '740')
        if type_code == '740':
            # Direct Waybill
            ET.SubElement(note, f"{{{self.ram}}}ContentCode").text = "D"
            ET.SubElement(note, f"{{{self.ram}}}Content").text = "Direct Shipment"
        else:
            # Master Waybill (House) - 741
            ET.SubElement(note, f"{{{self.ram}}}ContentCode").text = "C"
            ET.SubElement(note, f"{{{self.ram}}}Content").text = "CONSOLIDATED CARGO"

        # Add signatory consignor authentication
        consignor_auth = ET.SubElement(header, f"{{{self.ram}}}SignatoryConsignorAuthentication")
        ET.SubElement(consignor_auth, f"{{{self.ram}}}Signatory").text = "DUMMY SIGN"

        # Add signatory carrier authentication
        carrier_auth = ET.SubElement(header, f"{{{self.ram}}}SignatoryCarrierAuthentication")
        ET.SubElement(carrier_auth, f"{{{self.ram}}}ActualDateTime").text = waybill_info.get('issue_date', '')

    def _add_master_consignment(self, root, waybill_info):
        """Add MasterConsignment to the XML"""
        consignment = ET.SubElement(root, "ns0:MasterConsignment")

        # Add nil indicators
        ET.SubElement(consignment, f"{{{self.ram}}}NilCarriageValueIndicator").text = "true"
        ET.SubElement(consignment, f"{{{self.ram}}}NilCustomsValueIndicator").text = "true"
        ET.SubElement(consignment, f"{{{self.ram}}}NilInsuranceValueIndicator").text = "true"
        ET.SubElement(consignment, f"{{{self.ram}}}TotalChargePrepaidIndicator").text = "true"
        ET.SubElement(consignment, f"{{{self.ram}}}TotalDisbursementPrepaidIndicator").text = "true"

        # Add weight and volume
        weight_elem = ET.SubElement(consignment, f"{{{self.ram}}}IncludedTareGrossWeightMeasure")
        weight_elem.set("unitCode", "KGM")
        weight_elem.text = str(waybill_info.get('weight', 0.0))

        volume_elem = ET.SubElement(consignment, f"{{{self.ram}}}GrossVolumeMeasure")
        volume_elem.set("unitCode", "MTQ")
        volume_elem.text = str(waybill_info.get('volume', 0.0))

        # Add total pieces
        ET.SubElement(consignment, f"{{{self.ram}}}TotalPieceQuantity").text = str(waybill_info.get('piece_qty', 0))

        # Add consignor party
        self._add_consignor_party(consignment, waybill_info)

        # Add consignee party
        self._add_consignee_party(consignment, waybill_info)

        # Add freight forwarder party
        self._add_freight_forwarder_party(consignment, waybill_info)

        # Add origin location
        origin = ET.SubElement(consignment, f"{{{self.ram}}}OriginLocation")
        ET.SubElement(origin, f"{{{self.ram}}}ID").text = waybill_info.get('source_id', '')

        # Add destination location
        destination = ET.SubElement(consignment, f"{{{self.ram}}}FinalDestinationLocation")
        ET.SubElement(destination, f"{{{self.ram}}}ID").text = waybill_info.get('destination_id', '')

        # Add transport movement
        self._add_transport_movement(consignment, waybill_info)

        # Add handling instructions for SPH codes
        for code in waybill_info.get('special_handling_codes', []):
            handling = ET.SubElement(consignment, f"{{{self.ram}}}HandlingSPHInstructions")
            ET.SubElement(handling, f"{{{self.ram}}}DescriptionCode").text = code

        # Add accounting note
        accounting_note = ET.SubElement(consignment, f"{{{self.ram}}}IncludedAccountingNote")
        ET.SubElement(accounting_note, f"{{{self.ram}}}ContentCode").text = "GEN"
        ET.SubElement(accounting_note, f"{{{self.ram}}}Content").text = "PAYMENT BY CERTIFIED CHEQUE"

        # Add currency exchange
        currency = ET.SubElement(consignment, f"{{{self.ram}}}ApplicableOriginCurrencyExchange")
        ET.SubElement(currency, f"{{{self.ram}}}SourceCurrencyCode").text = "USD"

        # Add logistics allowance charges
        self._add_logistics_charges(consignment, waybill_info)

        # Add rating
        self._add_rating(consignment, waybill_info)

        # Add total rating
        self._add_total_rating(consignment, waybill_info)

    def _add_consignor_party(self, consignment, waybill_info):
        """Add ConsignorParty to the XML"""
        consignor = ET.SubElement(consignment, f"{{{self.ram}}}ConsignorParty")
        ET.SubElement(consignor, f"{{{self.ram}}}Name").text = "DUMMY CONSIGNOR"

        address = ET.SubElement(consignor, f"{{{self.ram}}}PostalStructuredAddress")
        ET.SubElement(address, f"{{{self.ram}}}PostcodeCode").text = "11430"
        source_id = waybill_info.get('source_id', 'AMS')
        ET.SubElement(address, f"{{{self.ram}}}StreetName").text = f"DUMMY STREET IN THE {source_id}"
        country_id_elem = ET.SubElement(address, f"{{{self.ram}}}CountryID")
        country_id_elem.set("schemeVersionID", "second edition 2006")
        country_id_elem.text = "NE"  # Netherlands for AMS

        contact = ET.SubElement(consignor, f"{{{self.ram}}}DefinedTradeContact")
        phone = ET.SubElement(contact, f"{{{self.ram}}}DirectTelephoneCommunication")
        ET.SubElement(phone, f"{{{self.ram}}}CompleteNumber").text = "0017182897272"

    def _add_consignee_party(self, consignment, waybill_info):
        """Add ConsigneeParty to the XML"""
        consignee = ET.SubElement(consignment, f"{{{self.ram}}}ConsigneeParty")
        ET.SubElement(consignee, f"{{{self.ram}}}Name").text = "DUMMY CONSIGNEE"

        address = ET.SubElement(consignee, f"{{{self.ram}}}PostalStructuredAddress")
        ET.SubElement(address, f"{{{self.ram}}}PostcodeCode").text = "65479"
        ET.SubElement(address, f"{{{self.ram}}}StreetName").text = "DUMMY STREET IN MALAWI"
        ET.SubElement(address, f"{{{self.ram}}}CityName").text = "LILONGWE"
        country_id_elem = ET.SubElement(address, f"{{{self.ram}}}CountryID")
        country_id_elem.set("schemeVersionID", "second edition 2006")
        country_id_elem.text = "MW"

        contact = ET.SubElement(consignee, f"{{{self.ram}}}DefinedTradeContact")
        phone = ET.SubElement(contact, f"{{{self.ram}}}DirectTelephoneCommunication")
        ET.SubElement(phone, f"{{{self.ram}}}CompleteNumber").text = "00265885297765"

    def _add_freight_forwarder_party(self, consignment, waybill_info):
        """Add FreightForwarderParty to the XML"""
        forwarder = ET.SubElement(consignment, f"{{{self.ram}}}FreightForwarderParty")
        ET.SubElement(forwarder, f"{{{self.ram}}}AdditionalID").text = "CAG"
        ET.SubElement(forwarder, f"{{{self.ram}}}Name").text = "AIR CARGO MALAWI LINITED"

        address = ET.SubElement(forwarder, f"{{{self.ram}}}FreightForwarderAddress")
        ET.SubElement(address, f"{{{self.ram}}}PostcodeCode").text = "11430"
        ET.SubElement(address, f"{{{self.ram}}}StreetName").text = "KAMUZU INTL AIRPORT"
        ET.SubElement(address, f"{{{self.ram}}}CityName").text = "LILONGWE"
        country_id_elem = ET.SubElement(address, f"{{{self.ram}}}CountryID")
        country_id_elem.set("schemeVersionID", "second edition 2006")
        country_id_elem.text = "MW"

    def _add_transport_movement(self, consignment, waybill_info):
        """Add SpecifiedLogisticsTransportMovement to the XML"""
        movement = ET.SubElement(consignment, f"{{{self.ram}}}SpecifiedLogisticsTransportMovement")
        ET.SubElement(movement, f"{{{self.ram}}}StageCode").text = "MAIN-CARRIAGE"
        ET.SubElement(movement, f"{{{self.ram}}}Mode").text = "AIR TRANSPORT"

        carrier = waybill_info.get('carrier', 'KQ')
        flight_number = waybill_info.get('flight_number', '777')
        ET.SubElement(movement, f"{{{self.ram}}}ID").text = f"{carrier}{flight_number}"
        ET.SubElement(movement, f"{{{self.ram}}}SequenceNumeric").text = "1"

        # Add transport means
        means = ET.SubElement(movement, f"{{{self.ram}}}UsedLogisticsTransportMeans")
        ET.SubElement(means, f"{{{self.ram}}}Name").text = carrier

        # Add arrival event
        arrival = ET.SubElement(movement, f"{{{self.ram}}}ArrivalEvent")
        arrival_location = ET.SubElement(arrival, f"{{{self.ram}}}OccurrenceArrivalLocation")
        ET.SubElement(arrival_location, f"{{{self.ram}}}ID").text = waybill_info.get('destination_id', '')
        ET.SubElement(arrival_location, f"{{{self.ram}}}TypeCode").text = "Airport"

        # Add departure event
        departure = ET.SubElement(movement, f"{{{self.ram}}}DepartureEvent")
        ET.SubElement(departure, f"{{{self.ram}}}ScheduledOccurrenceDateTime").text = "2025-06-12T01:00:00"

    def _add_logistics_charges(self, consignment, waybill_info):
        """Add ApplicableLogisticsAllowanceCharge elements to the XML"""
        charges = [
            ("MC", "4585.00"),
            ("CG", "190.00"),
            ("MA", "1834.00"),
            ("MY", "102704.00"),
            ("SC", "14672.00")
        ]

        for charge_id, amount in charges:
            charge = ET.SubElement(consignment, f"{{{self.ram}}}ApplicableLogisticsAllowanceCharge")
            ET.SubElement(charge, f"{{{self.ram}}}ID").text = charge_id
            ET.SubElement(charge, f"{{{self.ram}}}PrepaidIndicator").text = "true"
            ET.SubElement(charge, f"{{{self.ram}}}PartyTypeCode").text = "C"
            amount_elem = ET.SubElement(charge, f"{{{self.ram}}}ActualAmount")
            amount_elem.set("currencyID", "USD")
            amount_elem.text = amount

    def _add_rating(self, consignment, waybill_info):
        """Add ApplicableRating to the XML"""
        rating = ET.SubElement(consignment, f"{{{self.ram}}}ApplicableRating")
        ET.SubElement(rating, f"{{{self.ram}}}TypeCode").text = "F"
        total_charge_elem = ET.SubElement(rating, f"{{{self.ram}}}TotalChargeAmount")
        total_charge_elem.set("currencyID", "USD")
        total_charge_elem.text = "77907.00"

        item = ET.SubElement(rating, f"{{{self.ram}}}IncludedMasterConsignmentItem")
        ET.SubElement(item, f"{{{self.ram}}}SequenceNumeric").text = "1"
        type_code_elem = ET.SubElement(item, f"{{{self.ram}}}TypeCode")
        type_code_elem.set("listAgencyID", "1")

        weight_elem = ET.SubElement(item, f"{{{self.ram}}}GrossWeightMeasure")
        weight_elem.set("unitCode", "KGM")
        weight_elem.text = str(waybill_info.get('weight', 0.0))

        volume_elem = ET.SubElement(item, f"{{{self.ram}}}GrossVolumeMeasure")
        volume_elem.set("unitCode", "MTQ")
        volume_elem.text = str(waybill_info.get('volume', 0.0))

        ET.SubElement(item, f"{{{self.ram}}}PackageQuantity").text = str(waybill_info.get('piece_qty', 0))
        ET.SubElement(item, f"{{{self.ram}}}PieceQuantity").text = str(waybill_info.get('piece_qty', 0))
        ET.SubElement(item, f"{{{self.ram}}}Information").text = "NDA"

        nature = ET.SubElement(item, f"{{{self.ram}}}NatureIdentificationTransportCargo")
        ET.SubElement(nature, f"{{{self.ram}}}Identification").text = waybill_info.get('description', 'FILTER EQUIPMENT')

        # Add freight rate service charge
        service_charge = ET.SubElement(item, f"{{{self.ram}}}ApplicableFreightRateServiceCharge")
        ET.SubElement(service_charge, f"{{{self.ram}}}CategoryCode").text = "Q"
        chargeable_weight_elem = ET.SubElement(service_charge, f"{{{self.ram}}}ChargeableWeightMeasure")
        chargeable_weight_elem.set("unitCode", "KGM")
        chargeable_weight_elem.text = str(waybill_info.get('weight', 0.0))
        ET.SubElement(service_charge, f"{{{self.ram}}}AppliedRate").text = "153.00"
        applied_amount_elem = ET.SubElement(service_charge, f"{{{self.ram}}}AppliedAmount")
        applied_amount_elem.set("currencyID", "USD")
        applied_amount_elem.text = "77907.00"

    def _add_total_rating(self, consignment, waybill_info):
        """Add ApplicableTotalRating to the XML"""
        total_rating = ET.SubElement(consignment, f"{{{self.ram}}}ApplicableTotalRating")
        ET.SubElement(total_rating, f"{{{self.ram}}}TypeCode").text = "F"

        monetary_summation = ET.SubElement(total_rating, f"{{{self.ram}}}ApplicablePrepaidCollectMonetarySummation")
        ET.SubElement(monetary_summation, f"{{{self.ram}}}PrepaidIndicator").text = "true"

        # Add various charge amounts
        weight_charge_elem = ET.SubElement(monetary_summation, f"{{{self.ram}}}WeightChargeTotalAmount")
        weight_charge_elem.set("currencyID", "USD")
        weight_charge_elem.text = "77907.00"

        valuation_charge_elem = ET.SubElement(monetary_summation, f"{{{self.ram}}}ValuationChargeTotalAmount")
        valuation_charge_elem.set("currencyID", "USD")
        valuation_charge_elem.text = "0.00"

        tax_total_elem = ET.SubElement(monetary_summation, f"{{{self.ram}}}TaxTotalAmount")
        tax_total_elem.set("currencyID", "USD")
        tax_total_elem.text = "0.00"

        agent_due_elem = ET.SubElement(monetary_summation, f"{{{self.ram}}}AgentTotalDuePayableAmount")
        agent_due_elem.set("currencyID", "USD")
        agent_due_elem.text = "0.00"

        carrier_due_elem = ET.SubElement(monetary_summation, f"{{{self.ram}}}CarrierTotalDuePayableAmount")
        carrier_due_elem.set("currencyID", "USD")
        carrier_due_elem.text = "123985.00"

        grand_total_elem = ET.SubElement(monetary_summation, f"{{{self.ram}}}GrandTotalAmount")
        grand_total_elem.set("currencyID", "USD")
        grand_total_elem.text = "201892.00"

    def _format_xml(self, root):
        """Convert ElementTree to pretty-printed XML string"""
        try:
            # Use ElementTree's built-in pretty printing
            ET.indent(ET.ElementTree(root), space="  ")

            # Convert to string and manually fix the namespace issue
            rough_string = ET.tostring(root, 'utf-8').decode('utf-8')

            # Remove duplicate namespace declarations
            rough_string = rough_string.replace(' xmlns:ns0="iata:waybill:1" xmlns:ram="iata:datamodel:3"', '')
            rough_string = rough_string.replace('xmlns="iata:waybill:1" xmlns:ram="iata:datamodel:3" xmlns:ns0="iata:waybill:1" xmlns:ram="iata:datamodel:3"',
                                              'xmlns:ns0="iata:waybill:1" xmlns:ram="iata:datamodel:3"')

            # Add XML declaration
            xml_declaration = '<?xml version="1.0" encoding="UTF-8"?>\n'
            return xml_declaration + rough_string
        except Exception as e:
            logger.error(f"Error formatting XML: {str(e)}", exc_info=True)
            return f"<!-- Error formatting XML: {str(e)} -->"


def generate_xfwb_from_text(input_text, config=None):
    """Generate XFWB XML from the input text format"""
    try:
        # Add default values for required fields
        if not input_text or len(input_text.strip()) < 5:
            input_text = "706-50990704/740/KQ777/14JUN/AMSLLW/T1K260.0MC2.42/FILTER EQUIPMEN/EAP/SPX/HEA"

        generator = XFWBGenerator()
        result = generator.create_waybill_xml(input_text, config)
        return result
    except Exception as e:
        import traceback
        error_msg = f"Error generating XFWB XML: {str(e)}\n{traceback.format_exc()}"
        logger.error(error_msg)
        return f"<!-- {error_msg} -->"


if __name__ == "__main__":
    # Example input
    sample_input = "706-50990704/740/KQ777/14JUN/AMSLLW/T1K260.0MC2.42/FILTER EQUIPMEN/EAP/SPX/HEA"

    # Generate XML
    xml_output = generate_xfwb_from_text(sample_input)

    # Print or save to file
    print(xml_output)

    # Optionally save to file
    with open("generated_waybill.xml", "w", encoding="utf-8") as f:
        f.write(xml_output)
