#!/usr/bin/env python3
"""
Random Data Generator for IATA XML Generator
Generates realistic cargo/logistics business data for testing and demonstration purposes.
"""

import random
import string
from datetime import datetime, timedelta

class RandomDataGenerator:
    def __init__(self):
        # Realistic cargo/logistics company names
        self.company_prefixes = [
            "Global", "International", "Express", "Rapid", "Swift", "Prime", "Elite", "Premier",
            "United", "Allied", "Continental", "Trans", "Inter", "Euro", "Asia", "Pacific",
            "Atlantic", "World", "Universal", "Dynamic", "Advanced", "Professional", "Reliable",
            "Secure", "Fast", "Direct", "Efficient", "Strategic", "Integrated", "Consolidated"
        ]
        
        self.company_types = [
            "Logistics", "Freight", "Cargo", "Shipping", "Transport", "Express", "Air Cargo",
            "Supply Chain", "Distribution", "Forwarding", "International", "Global Trade",
            "Courier Services", "Air Services", "Cargo Solutions", "Trade Solutions"
        ]
        
        self.company_suffixes = [
            "Ltd", "Limited", "Inc", "Corporation", "Corp", "Co", "Company", "Group",
            "International", "Worldwide", "Global", "Services", "Solutions", "Systems"
        ]

        # Major cargo cities worldwide
        self.cities_data = {
            "US": [
                {"city": "New York", "state": "NY", "postal": "10001"},
                {"city": "Los Angeles", "state": "CA", "postal": "90001"},
                {"city": "Chicago", "state": "IL", "postal": "60601"},
                {"city": "Miami", "state": "FL", "postal": "33101"},
                {"city": "Atlanta", "state": "GA", "postal": "30301"}
            ],
            "GB": [
                {"city": "London", "postal": "SW1A 1AA"},
                {"city": "Manchester", "postal": "M1 1AA"},
                {"city": "Birmingham", "postal": "B1 1AA"}
            ],
            "DE": [
                {"city": "Frankfurt", "postal": "60311"},
                {"city": "Hamburg", "postal": "20095"},
                {"city": "Munich", "postal": "80331"}
            ],
            "CN": [
                {"city": "Shanghai", "postal": "200000"},
                {"city": "Beijing", "postal": "100000"},
                {"city": "Guangzhou", "postal": "510000"}
            ],
            "SG": [
                {"city": "Singapore", "postal": "018956"}
            ],
            "AE": [
                {"city": "Dubai", "postal": "00000"},
                {"city": "Abu Dhabi", "postal": "00000"}
            ]
        }

        # Country names mapping
        self.country_names = {
            "US": "United States",
            "GB": "United Kingdom", 
            "DE": "Germany",
            "CN": "China",
            "SG": "Singapore",
            "AE": "United Arab Emirates"
        }

    def generate_company_name(self):
        """Generate a realistic cargo/logistics company name"""
        prefix = random.choice(self.company_prefixes)
        company_type = random.choice(self.company_types)
        suffix = random.choice(self.company_suffixes)
        
        patterns = [
            f"{prefix} {company_type} {suffix}",
            f"{prefix} {company_type}",
            f"{company_type} {suffix}",
            f"{prefix} {suffix}"
        ]
        
        return random.choice(patterns).upper()

    def generate_address(self, country_code=None):
        """Generate a realistic address for the given country"""
        if not country_code:
            country_code = random.choice(list(self.cities_data.keys()))
        
        if country_code not in self.cities_data:
            country_code = "US"
        
        city_data = random.choice(self.cities_data[country_code])
        street_number = random.randint(1, 9999)
        street_names = ["Cargo Way", "Freight Avenue", "Logistics Drive", "Industrial Road", "Commerce Street"]
        street_name = random.choice(street_names)
        
        address = f"{street_number} {street_name}"
        
        return {
            "address": address,
            "city": city_data["city"],
            "postal_code": city_data["postal"],
            "country_code": country_code,
            "country_name": self.country_names[country_code]
        }

    def generate_account_id(self):
        """Generate a realistic account/reference ID"""
        patterns = [
            f"ACC{random.randint(100000, 999999)}",
            f"{random.randint(1000000, 9999999)}",
            f"REF{random.randint(10000, 99999)}",
            f"{''.join(random.choices(string.ascii_uppercase, k=3))}{random.randint(1000, 9999)}"
        ]
        return random.choice(patterns)

    def generate_complete_company_data(self, country_code=None):
        """Generate complete company data including all contact information"""
        company_name = self.generate_company_name()
        address_data = self.generate_address(country_code)
        account_id = self.generate_account_id()
        
        return {
            "company_name": company_name,
            "address": address_data["address"],
            "city": address_data["city"],
            "postal_code": address_data["postal_code"],
            "country_code": address_data["country_code"],
            "country_name": address_data["country_name"],
            "account_id": account_id
        }

    def generate_cargo_parties(self):
        """Generate a complete set of cargo parties (consignor, consignee, forwarder)"""
        countries = list(self.cities_data.keys())
        
        consignor_country = random.choice(countries)
        consignee_country = random.choice([c for c in countries if c != consignor_country])
        forwarder_country = random.choice(countries)
        
        return {
            "consignor": self.generate_complete_company_data(consignor_country),
            "consignee": self.generate_complete_company_data(consignee_country),
            "freight_forwarder": self.generate_complete_company_data(forwarder_country)
        }

def generate_random_cargo_data():
    """Convenience function to generate random cargo data"""
    generator = RandomDataGenerator()
    return generator.generate_cargo_parties()

if __name__ == "__main__":
    generator = RandomDataGenerator()
    data = generator.generate_cargo_parties()
    
    print("=== Random Cargo Data Generated ===")
    print(f"Consignor: {data['consignor']['company_name']}")
    print(f"Consignee: {data['consignee']['company_name']}")
    print(f"Freight Forwarder: {data['freight_forwarder']['company_name']}")
