.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.xml-output {
  border: 1px solid #dee2e6;
  border-radius: 0.25rem;
  min-height: 300px;
  max-height: 500px;
  overflow-y: auto;
}

.empty-output {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
  background-color: #f8f9fa;
  color: #6c757d;
  font-style: italic;
}

pre {
  margin: 0 !important;
  border-radius: 0.25rem;
}

footer {
  margin-top: auto;
}

.debug-info {
  background-color: #f8f9fa;
  padding: 15px;
  border-radius: 5px;
  margin-bottom: 15px;
}

.logs {
  background-color: #212529;
  border-radius: 5px;
  max-height: 300px;
  overflow-y: auto;
}

.accordion-button:not(.collapsed) {
  background-color: #e7f1ff;
  color: #0c63e4;
}
