import { useState } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, Spinner, <PERSON><PERSON>, <PERSON>v<PERSON>, Nav, Accordion, Badge } from 'react-bootstrap';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { tomorrow } from 'react-syntax-highlighter/dist/esm/styles/prism';
import axios from 'axios';
import 'bootstrap/dist/css/bootstrap.min.css';
import './App.css';

function App() {
  const [inputText, setInputText] = useState('');
  const [xmlOutput, setXmlOutput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [successMessage, setSuccessMessage] = useState('');
  const [debugInfo, setDebugInfo] = useState(null);
  const [showDebug, setShowDebug] = useState(false);
  const [logs, setLogs] = useState('');

  // API base URL
  const API_BASE_URL = 'http://localhost:5001';

  // Load sample data
  const loadSample = async () => {
    try {
      setIsLoading(true);
      const response = await axios.get(`${API_BASE_URL}/api/sample`);
      setInputText(response.data.sample);
      setSuccessMessage('Sample data loaded successfully');
      setTimeout(() => setSuccessMessage(''), 3000);
    } catch (err) {
      setError('Failed to load sample data: ' + err.message);
      setTimeout(() => setError(null), 5000);
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch debug info
  const fetchDebugInfo = async () => {
    try {
      setIsLoading(true);
      const response = await axios.get(`${API_BASE_URL}/api/debug/generator-info`);
      setDebugInfo(response.data);
      setShowDebug(true);
    } catch (err) {
      setError('Failed to fetch debug info: ' + (err.response?.data?.error || err.message));
      setTimeout(() => setError(null), 5000);
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch logs
  const fetchLogs = async () => {
    try {
      setIsLoading(true);
      const response = await axios.get(`${API_BASE_URL}/api/logs`);
      setLogs(response.data.logs);
      setShowDebug(true);
    } catch (err) {
      setError('Failed to fetch logs: ' + (err.response?.data?.error || err.message));
      setTimeout(() => setError(null), 5000);
    } finally {
      setIsLoading(false);
    }
  };

  // Generate XML from input text
  const generateXml = async () => {
    if (!inputText.trim()) {
      setError('Please enter some input text');
      setTimeout(() => setError(null), 5000);
      return;
    }

    try {
      setIsLoading(true);
      console.log('Sending request to generate XML with input:', inputText.substring(0, 100) + '...');

      const response = await axios.post(`${API_BASE_URL}/api/generate-xml`, {
        input_text: inputText
      });

      console.log('Response received:', response.data);
      setXmlOutput(response.data.xml);
      setSuccessMessage('XML generated successfully');
      setTimeout(() => setSuccessMessage(''), 3000);
    } catch (err) {
      console.error('Error generating XML:', err);
      let errorMessage = 'Failed to generate XML: ';

      if (err.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        console.error('Error response data:', err.response.data);
        console.error('Error response status:', err.response.status);
        errorMessage += err.response.data?.error || err.message;
      } else if (err.request) {
        // The request was made but no response was received
        console.error('Error request:', err.request);
        errorMessage += 'No response received from server. Check if the backend is running.';
      } else {
        // Something happened in setting up the request that triggered an Error
        console.error('Error message:', err.message);
        errorMessage += err.message;
      }

      setError(errorMessage);
      setTimeout(() => setError(null), 8000);

      // Automatically fetch debug info when an error occurs
      try {
        const debugResponse = await axios.get(`${API_BASE_URL}/api/debug/generator-info`);
        setDebugInfo(debugResponse.data);
        setShowDebug(true);
      } catch (debugErr) {
        console.error('Failed to fetch debug info:', debugErr);
      }
    } finally {
      setIsLoading(false);
    }
  };

  // Download XML as file
  const downloadXml = () => {
    if (!xmlOutput) {
      setError('No XML to download. Generate XML first.');
      setTimeout(() => setError(null), 5000);
      return;
    }

    const blob = new Blob([xmlOutput], { type: 'application/xml' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'flight_manifest.xml';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    setSuccessMessage('XML downloaded successfully');
    setTimeout(() => setSuccessMessage(''), 3000);
  };

  // Clear all fields
  const clearFields = () => {
    setInputText('');
    setXmlOutput('');
    setSuccessMessage('Fields cleared');
    setTimeout(() => setSuccessMessage(''), 3000);
  };

  return (
    <div className="app">
      <Navbar bg="dark" variant="dark" expand="lg" className="mb-4">
        <Container>
          <Navbar.Brand href="#home">IATA XML Generator</Navbar.Brand>
          <Navbar.Toggle aria-controls="basic-navbar-nav" />
          <Navbar.Collapse id="basic-navbar-nav">
            <Nav className="ms-auto">
              <Nav.Link href="#" onClick={loadSample}>Load Sample</Nav.Link>
              <Nav.Link href="#" onClick={clearFields}>Clear All</Nav.Link>
            </Nav>
          </Navbar.Collapse>
        </Container>
      </Navbar>

      <Container>
        {error && <Alert variant="danger">{error}</Alert>}
        {successMessage && <Alert variant="success">{successMessage}</Alert>}

        <Row className="mb-4">
          <Col>
            <h1 className="text-center mb-4">Flight Manifest XML Generator</h1>
            <p className="text-center">
              Convert flight manifest data in condensed format to IATA XML format.
              Enter the manifest data in the input field below, then click 'Generate XML'.
            </p>
          </Col>
        </Row>

        <Row className="mb-4">
          <Col>
            <Form.Group className="mb-3">
              <Form.Label><h4>Input - Flight Manifest Data</h4></Form.Label>
              <Form.Control
                as="textarea"
                rows={10}
                value={inputText}
                onChange={(e) => setInputText(e.target.value)}
                placeholder="Enter flight manifest data here..."
              />
            </Form.Group>

            <div className="d-flex gap-2 mb-4">
              <Button
                variant="primary"
                onClick={generateXml}
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <Spinner
                      as="span"
                      animation="border"
                      size="sm"
                      role="status"
                      aria-hidden="true"
                    />
                    <span className="ms-2">Generating...</span>
                  </>
                ) : (
                  'Generate XML'
                )}
              </Button>
              <Button
                variant="success"
                onClick={downloadXml}
                disabled={!xmlOutput || isLoading}
              >
                Download XML
              </Button>
              <Button
                variant="secondary"
                onClick={loadSample}
                disabled={isLoading}
              >
                Load Sample
              </Button>
              <Button
                variant="danger"
                onClick={clearFields}
                disabled={isLoading}
              >
                Clear
              </Button>
            </div>
          </Col>
        </Row>

        <Row>
          <Col>
            <Form.Group className="mb-3">
              <Form.Label><h4>Output - Generated XML</h4></Form.Label>
              <div className="xml-output">
                {xmlOutput ? (
                  <SyntaxHighlighter
                    language="xml"
                    style={tomorrow}
                    showLineNumbers={true}
                    wrapLines={true}
                  >
                    {xmlOutput}
                  </SyntaxHighlighter>
                ) : (
                  <div className="empty-output">
                    <p>XML output will appear here after generation</p>
                  </div>
                )}
              </div>
            </Form.Group>
          </Col>
        </Row>
      </Container>

      {/* Debug Section */}
      {showDebug && (
        <Row className="mt-4">
          <Col>
            <Accordion defaultActiveKey="0">
              <Accordion.Item eventKey="0">
                <Accordion.Header>
                  Debug Information <Badge bg="info">Developer Tools</Badge>
                </Accordion.Header>
                <Accordion.Body>
                  <Row>
                    <Col md={6}>
                      <h5>Generator Module Info</h5>
                      {debugInfo ? (
                        <div className="debug-info">
                          <p><strong>Path:</strong> {debugInfo.generator_path}</p>
                          <p><strong>File Exists:</strong> {debugInfo.file_exists ? 'Yes' : 'No'}</p>
                          <p><strong>File Size:</strong> {debugInfo.file_size} bytes</p>
                          <p><strong>Function Exists:</strong> {debugInfo.function_exists ? 'Yes' : 'No'}</p>
                          <h6>Function Source Preview:</h6>
                          <SyntaxHighlighter
                            language="python"
                            style={tomorrow}
                            showLineNumbers={true}
                            wrapLines={true}
                            customStyle={{maxHeight: '200px'}}
                          >
                            {debugInfo.function_source_preview}
                          </SyntaxHighlighter>
                        </div>
                      ) : (
                        <p>No debug info available. <Button size="sm" onClick={fetchDebugInfo}>Fetch Debug Info</Button></p>
                      )}
                    </Col>
                    <Col md={6}>
                      <h5>Server Logs</h5>
                      {logs ? (
                        <div className="logs">
                          <SyntaxHighlighter
                            language="text"
                            style={tomorrow}
                            showLineNumbers={false}
                            wrapLines={true}
                            customStyle={{maxHeight: '300px'}}
                          >
                            {logs}
                          </SyntaxHighlighter>
                        </div>
                      ) : (
                        <p>No logs available. <Button size="sm" onClick={fetchLogs}>Fetch Logs</Button></p>
                      )}
                    </Col>
                  </Row>
                  <div className="text-center mt-3">
                    <Button variant="secondary" size="sm" onClick={() => setShowDebug(false)}>Hide Debug Info</Button>
                  </div>
                </Accordion.Body>
              </Accordion.Item>
            </Accordion>
          </Col>
        </Row>
      )}

      <Row className="mt-4">
        <Col className="text-center">
          <Button
            variant="link"
            onClick={() => setShowDebug(!showDebug)}
            className="text-muted"
          >
            {showDebug ? 'Hide Developer Tools' : 'Show Developer Tools'}
          </Button>
        </Col>
      </Row>

      <footer className="bg-dark text-white text-center py-3 mt-5">
        <Container>
          <p className="mb-0">IATA XML Generator &copy; {new Date().getFullYear()}</p>
        </Container>
      </footer>
    </div>
  );
}

export default App;
