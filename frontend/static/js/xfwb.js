document.addEventListener('DOMContentLoaded', function() {
    // API base URL - use the current origin
    const API_BASE_URL = window.location.origin;

    // DOM elements
    const inputTextarea = document.getElementById('xfwb-input');
    const parseBtn = document.getElementById('parse-btn');
    const loadSampleBtn = document.getElementById('load-sample-btn');
    const clearBtn = document.getElementById('clear-btn');
    const generateBtn = document.getElementById('generate-btn');
    const copyBtn = document.getElementById('copy-btn');
    const downloadBtn = document.getElementById('download-btn');
    const xmlOutput = document.getElementById('xml-output');
    const parseResult = document.getElementById('parse-result');
    const statusContainer = document.getElementById('status-container');

    // Tab functionality
    const tabs = document.querySelectorAll('.tab');
    tabs.forEach(tab => {
        tab.addEventListener('click', () => {
            // Remove active class from all tabs and content
            document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
            document.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));

            // Add active class to clicked tab
            tab.classList.add('active');

            // Show corresponding content
            const tabId = tab.getAttribute('data-tab');
            document.getElementById(tabId).classList.add('active');
        });
    });

    // Show status message
    function showStatus(message, type = 'info') {
        const statusDiv = document.createElement('div');
        statusDiv.className = `status-message status-${type}`;
        statusDiv.textContent = message;

        // Clear previous status messages
        statusContainer.innerHTML = '';
        statusContainer.appendChild(statusDiv);

        // Auto-clear after 5 seconds
        setTimeout(() => {
            statusDiv.remove();
        }, 5000);
    }

    // Load sample data
    loadSampleBtn.addEventListener('click', () => {
        fetch(`${API_BASE_URL}/api/sample-xfwb`)
            .then(response => response.json())
            .then(data => {
                if (data.success && data.sample) {
                    inputTextarea.value = data.sample;
                    showStatus('Sample data loaded', 'success');
                } else {
                    showStatus('Failed to load sample data', 'error');
                }
            })
            .catch(error => {
                console.error('Error loading sample:', error);
                // Fallback to local sample data if API fails
                inputTextarea.value = "706-51663054NBOLLW/T1K138.0MC1.01/HUMAN REMAINS\n/HUM\n706-60609942NBOLLW/T4K25.0MC0.01/DHL EXPRESS\n/COU";
                showStatus('Using local sample data', 'success');
            });
    });

    // Clear all fields
    clearBtn.addEventListener('click', () => {
        inputTextarea.value = '';
        xmlOutput.value = '';
        parseResult.innerHTML = '';
        showStatus('All fields cleared', 'info');
    });

    // Parse XFWB data
    parseBtn.addEventListener('click', () => {
        const inputText = inputTextarea.value.trim();

        if (!inputText) {
            showStatus('Please enter XFWB data', 'error');
            return;
        }

        try {
            // Parse the input text
            parseXFWBData(inputText);
        } catch (error) {
            showStatus(`Error parsing data: ${error.message}`, 'error');
        }
    });

    // Parse XFWB data function
    function parseXFWBData(inputText) {
        try {
            // Split by lines in case multiple entries are provided
            const lines = inputText.trim().split('\n');

            // Array to store all parsed AWBs
            const parsedAwbs = [];
            let currentAwb = null;

            // Process all lines to find AWBs and their Handling Instructions codes
            for (let i = 0; i < lines.length; i++) {
                const line = lines[i].trim();

                // Skip empty lines
                if (!line) continue;

                // Extract AWB number, origin, destination, pieces, weight, volume, and description
                // Pattern: {prefix}-{number}{origin}{destination}/T{pieces}K{weight}MC{volume}/{description}
                const awbPattern = /(\d{3})-(\d+)([A-Z]{3})([A-Z]{3})\/([ST])(\d+)K(\d+\.\d+)MC(\d+\.\d+)\/(.*)/;
                const match = line.match(awbPattern);

                if (match) {
                    // This is a new AWB line
                    const prefix = match[1];
                    const number = match[2];
                    const origin = match[3];
                    const destination = match[4];
                    const splitCode = match[5];  // S or T
                    const pieces = parseInt(match[6]);
                    const weight = parseFloat(match[7]);
                    const volume = parseFloat(match[8]);
                    const description = match[9].trim();

                    // Create a new AWB object
                    currentAwb = {
                        awbNumber: `${prefix}-${number}`,
                        prefix,
                        number,
                        origin,
                        destination,
                        pieces,
                        weight,
                        volume,
                        description,
                        specialHandlingCodes: [],
                        splitCode
                    };

                    // Add to the list of AWBs
                    parsedAwbs.push(currentAwb);
                } else if (line.startsWith('/') && currentAwb) {
                    // This is a Handling Instructions code line for the current AWB
                    const codes = line.substring(1).split('/');
                    currentAwb.specialHandlingCodes.push(...codes.filter(code => code.trim()));
                }
            }

            // Check if we found any AWBs
            if (parsedAwbs.length === 0) {
                showStatus('No valid AWBs found in the input', 'error');
                return;
            }

            // Use the first AWB for the form fields
            const primaryAwb = parsedAwbs[0];

            // Populate form fields with parsed data from the first AWB
            document.getElementById('message-id').value = primaryAwb.awbNumber;

            // Create current date/time in ISO format
            const now = new Date();
            document.getElementById('issue-date').value = now.toISOString();

            // Build the parse result HTML
            let resultHtml = '';

            if (parsedAwbs.length === 1) {
                // Single AWB
                resultHtml = `
                    <p><strong>Parsed data:</strong> AWB ${primaryAwb.awbNumber}</p>
                    <p><strong>Origin:</strong> ${primaryAwb.origin}</p>
                    <p><strong>Destination:</strong> ${primaryAwb.destination}</p>
                    <p><strong>Pieces:</strong> ${primaryAwb.pieces}</p>
                    <p><strong>Weight:</strong> ${primaryAwb.weight.toFixed(2)} KG</p>
                    <p><strong>Volume:</strong> ${primaryAwb.volume.toFixed(2)} m³</p>
                    <p><strong>Description:</strong> ${primaryAwb.description}</p>
                    <p><strong>Handling Instructions Codes:</strong> ${primaryAwb.specialHandlingCodes.join(', ') || 'None'}</p>
                `;
            } else {
                // Multiple AWBs
                resultHtml = `<p><strong>Parsed ${parsedAwbs.length} AWBs:</strong></p><ul>`;

                parsedAwbs.forEach(awb => {
                    resultHtml += `
                        <li>
                            <strong>AWB ${awb.awbNumber}</strong> -
                            ${awb.origin} to ${awb.destination},
                            ${awb.pieces} pieces,
                            ${awb.weight.toFixed(2)} KG,
                            ${awb.volume.toFixed(2)} m³,
                            "${awb.description}"
                            ${awb.specialHandlingCodes.length > 0 ?
                                ` (Codes: ${awb.specialHandlingCodes.join(', ')})` : ''}
                        </li>
                    `;
                });

                resultHtml += '</ul>';
            }

            // Display parse result
            parseResult.innerHTML = resultHtml;

            // Store parsed data for later use
            window.parsedXFWBData = {
                awbs: parsedAwbs,
                primaryAwb: primaryAwb
            };

            // Switch to config tab
            document.querySelector('.tab[data-tab="config"]').click();

            showStatus(`Successfully parsed ${parsedAwbs.length} AWB${parsedAwbs.length > 1 ? 's' : ''}`, 'success');
        } catch (error) {
            console.error('Error parsing XFWB data:', error);
            showStatus(`Error parsing data: ${error.message}`, 'error');
        }
    }

    // Generate XML
    generateBtn.addEventListener('click', () => {
        // Check if we have parsed data
        if (!window.parsedXFWBData) {
            showStatus('Please parse XFWB data first', 'error');
            document.querySelector('.tab[data-tab="input"]').click();
            return;
        }

        // Get form values for configuration
        const config = {
            issue_date: document.getElementById('issue-date').value,
            consignor_name: document.getElementById('consignor-name').value,
            consignor_address: document.getElementById('consignor-address').value,
            consignor_city: document.getElementById('consignor-city').value,
            consignor_country: document.getElementById('consignor-country').value,
            consignor_country_code: document.getElementById('consignor-country-code').value,
            consignor_postcode: document.getElementById('consignor-postcode').value,
            consignee_name: document.getElementById('consignee-name').value,
            consignee_address: document.getElementById('consignee-address').value,
            consignee_city: document.getElementById('consignee-city').value,
            consignee_country: document.getElementById('consignee-country').value,
            consignee_country_code: document.getElementById('consignee-country-code').value,
            forwarder_name: document.getElementById('forwarder-name').value,
            forwarder_id: document.getElementById('forwarder-id').value,
            forwarder_address: document.getElementById('forwarder-address').value,
            forwarder_city: document.getElementById('forwarder-city').value,
            forwarder_country: document.getElementById('forwarder-country').value,
            forwarder_country_code: document.getElementById('forwarder-country-code').value,
            forwarder_postcode: document.getElementById('forwarder-postcode').value
        };

        // For backward compatibility, if we have a primary AWB, use its number
        if (window.parsedXFWBData.primaryAwb) {
            config.awb_number = window.parsedXFWBData.primaryAwb.awbNumber;
        }

        // Check if we have multiple AWBs
        const awbCount = window.parsedXFWBData.awbs ? window.parsedXFWBData.awbs.length : 0;

        if (awbCount > 1) {
            showStatus(`Generating XML for ${awbCount} AWBs...`, 'info');
        } else {
            showStatus('Generating XML...', 'info');
        }

        // Generate XML
        generateXFWBXml(inputTextarea.value, config);
    });

    // Generate XFWB XML function
    function generateXFWBXml(inputText, config) {
        fetch(`${API_BASE_URL}/api/generate-xfwb`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                input_text: inputText,
                config: config
            })
        })
        .then(response => {
            // Check if the response is valid JSON
            const contentType = response.headers.get('content-type');
            if (contentType && contentType.includes('application/json')) {
                return response.json();
            } else {
                // If not JSON, get the text and create an error
                return response.text().then(text => {
                    throw new Error(`Server returned non-JSON response: ${text.substring(0, 100)}...`);
                });
            }
        })
        .then(data => {
            if (data.success && data.xml) {
                xmlOutput.value = data.xml;
                document.querySelector('.tab[data-tab="output"]').click();

                // Check if we have multiple AWBs
                if (data.awb_count && data.awb_count > 1) {
                    showStatus(`XML generated successfully for ${data.awb_count} AWBs`, 'success');
                } else {
                    showStatus('XML generated successfully', 'success');
                }
            } else {
                showStatus(`Failed to generate XML: ${data.error || 'Unknown error'}`, 'error');
            }
        })
        .catch(error => {
            console.error('Error generating XML:', error);

            // Check if the error message contains HTML or XML
            const errorMsg = error.message || '';
            if (errorMsg.includes('<!DOCTYPE') || errorMsg.includes('<html') || errorMsg.includes('<?xml')) {
                showStatus('Error: Server returned HTML/XML instead of JSON. Please check server logs.', 'error');
            } else {
                showStatus(`Error generating XML: ${errorMsg}`, 'error');
            }
        });
    }

    // Copy to clipboard
    copyBtn.addEventListener('click', () => {
        if (!xmlOutput.value) {
            showStatus('No XML to copy', 'error');
            return;
        }

        xmlOutput.select();
        document.execCommand('copy');
        showStatus('XML copied to clipboard', 'success');
    });

    // Download XML
    downloadBtn.addEventListener('click', () => {
        if (!xmlOutput.value) {
            showStatus('No XML to download', 'error');
            return;
        }

        const blob = new Blob([xmlOutput.value], { type: 'application/xml' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `waybill_${new Date().getTime()}.xml`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        showStatus('XML downloaded successfully', 'success');
    });
});
