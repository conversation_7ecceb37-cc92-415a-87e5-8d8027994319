/* Common styles for XML Generator application */
:root {
    --primary-color: #0056b3;
    --secondary-color: #003d82;
    --accent-color: #4cc9f0;
    --dark-color: #212529;
    --light-color: #f8f9fa;
}

body {
    font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
    background-color: #f5f7ff;
    color: var(--dark-color);
    line-height: 1.6;
}

.container {
    max-width: 1400px;
    margin: 2rem auto;
    background-color: white;
    padding: 2.5rem;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
}

.header {
    text-align: center;
    margin-bottom: 2.5rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

h1 {
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.lead {
    font-size: 1.1rem;
    color: #6c757d;
    max-width: 700px;
    margin: 0 auto;
}

.form-label {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--dark-color);
}

.form-control, .form-select {
    padding: 0.75rem 1rem;
    border-radius: 8px;
    border: 1px solid #dee2e6;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: var(--accent-color);
    box-shadow: 0 0 0 0.25rem rgba(0, 86, 179, 0.15);
}

textarea.form-control {
    min-height: 300px;
    resize: vertical;
}

.btn {
    padding: 0.65rem 1.5rem;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
    transform: translateY(-1px);
}

.btn-secondary {
    background-color: #6c757d;
    border-color: #6c757d;
}

.btn-secondary:hover {
    background-color: #5a6268;
    border-color: #545b62;
    transform: translateY(-1px);
}

.btn-success {
    background-color: #2a9d8f;
    border-color: #2a9d8f;
}

.btn-success:hover {
    background-color: #21867a;
    border-color: #1f7d72;
    transform: translateY(-1px);
}

.xml-output {
    background-color: #f8fafc;
    padding: 1.25rem;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
    font-family: 'Courier New', monospace;
    white-space: pre-wrap;
    max-height: 500px;
    overflow-y: auto;
    color: #334155;
    font-size: 0.9rem;
    line-height: 1.7;
}

.xml-output::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

.xml-output::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 10px;
}

.xml-output::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 10px;
}

.xml-output::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

.action-buttons {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
}

.footer {
    text-align: center;
    margin-top: 3rem;
    padding-top: 1.5rem;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    color: #6c757d;
    font-size: 0.9rem;
}

@media (max-width: 768px) {
    .container {
        padding: 1.5rem;
    }

    .header {
        margin-bottom: 1.5rem;
    }

    h1 {
        font-size: 1.8rem;
    }
}

/* Animation for the generate button */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.btn-pulse {
    animation: pulse 2s infinite;
}

/* Status indicators */
.status {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
    display: inline-block;
    margin-bottom: 1rem;
}

.status-processing {
    background-color: #fff3bf;
    color: #e67700;
}

.status-success {
    background-color: #d3f9d8;
    color: #2b8a3e;
}

.status-error {
    background-color: #ffc9c9;
    color: #c92a2a;
}

/* Tabs styling */
.tabs {
    display: flex;
    margin-bottom: 20px;
}

.tab {
    padding: 10px 20px;
    background-color: #e0e0e0;
    cursor: pointer;
    border-radius: 4px 4px 0 0;
    margin-right: 5px;
}

.tab.active {
    background-color: var(--primary-color);
    color: white;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* Navbar styling */
.navbar {
    background-color: #343a40;
    padding: 10px 0;
    margin-bottom: 20px;
}

.navbar-brand {
    color: white;
    font-size: 1.5rem;
    font-weight: bold;
    text-decoration: none;
    padding: 0 15px;
}

.navbar-nav {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
}

.nav-item {
    margin-right: 15px;
}

.nav-link {
    color: rgba(255,255,255,0.8);
    text-decoration: none;
    padding: 8px 15px;
    border-radius: 4px;
}

.nav-link:hover, .nav-link.active {
    color: white;
    background-color: rgba(255,255,255,0.1);
}

/* Status message styling */
.status-message {
    padding: 10px;
    margin: 10px 0;
    border-radius: 4px;
}

.status-success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status-error {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.status-info {
    background-color: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

/* Button group styling */
.button-group {
    display: flex;
    gap: 10px;
    margin-top: 10px;
}

/* Field group styling */
.field-group {
    margin-bottom: 15px;
}
