<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>XFWB XML Generator</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="css/styles.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
        }
        h1 {
            color: #0056b3;
            text-align: center;
            margin-bottom: 20px;
        }
        .input-section, .output-section {
            margin-bottom: 20px;
        }
        textarea {
            width: 100%;
            min-height: 200px;
            border: 1px solid #ccc;
            border-radius: 4px;
            padding: 10px;
            font-family: monospace;
            font-size: 14px;
            resize: vertical;
        }
        button {
            background-color: #0056b3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-top: 10px;
        }
        button:hover {
            background-color: #003d82;
        }
        .output-section {
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 4px;
        }
        .error {
            color: red;
            font-weight: bold;
        }
        .field-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        .row {
            display: flex;
            gap: 20px;
        }
        .col {
            flex: 1;
        }
        .tabs {
            display: flex;
            margin-bottom: 20px;
        }
        .tab {
            padding: 10px 20px;
            background-color: #e0e0e0;
            cursor: pointer;
            border-radius: 4px 4px 0 0;
            margin-right: 5px;
        }
        .tab.active {
            background-color: #0056b3;
            color: white;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
        .navbar {
            background-color: #343a40;
            padding: 10px 0;
            margin-bottom: 20px;
        }
        .navbar-brand {
            color: white;
            font-size: 1.5rem;
            font-weight: bold;
            text-decoration: none;
            padding: 0 15px;
        }
        .navbar-nav {
            display: flex;
            list-style: none;
            margin: 0;
            padding: 0;
        }
        .nav-item {
            margin-right: 15px;
        }
        .nav-link {
            color: rgba(255,255,255,0.8);
            text-decoration: none;
            padding: 8px 15px;
            border-radius: 4px;
        }
        .nav-link:hover, .nav-link.active {
            color: white;
            background-color: rgba(255,255,255,0.1);
        }
        .status-message {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .status-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status-error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status-info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <nav class="navbar">
        <div class="container">
            <a class="navbar-brand" href="index.html">IATA XML Generator</a>
            <ul class="navbar-nav">
                <li class="nav-item">
                    <a class="nav-link" href="index.html">Flight Manifest</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link active" href="xfwb.html">Master Waybill</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="xfzb.html">House Waybill</a>
                </li>
            </ul>
        </div>
    </nav>

    <div class="container">
        <h1>XFWB XML Generator</h1>

        <div class="tabs">
            <div class="tab active" data-tab="input">Input Data</div>
            <div class="tab" data-tab="config">Configuration</div>
            <div class="tab" data-tab="output">XML Output</div>
        </div>

        <div id="status-container"></div>

        <div class="tab-content active" id="input">
            <div class="input-section">
                <label for="xfwb-input">Paste XFWB Data:</label>
                <textarea id="xfwb-input" placeholder="Enter XFWB data (e.g., 706-51663054NBOLLW/T1K138.0MC1.01/HUMAN REMAINS)"></textarea>
                <div class="button-group">
                    <button id="parse-btn">Parse Data</button>
                    <button id="load-sample-btn">Load Sample</button>
                    <button id="clear-btn">Clear</button>
                </div>
                <div id="parse-result" class="field-group"></div>
            </div>
        </div>

        <div class="tab-content" id="config">
            <div class="row">
                <div class="col">
                    <h3>Message Header</h3>
                    <div class="field-group">
                        <label for="message-id">Message ID:</label>
                        <input type="text" id="message-id" value="">
                    </div>
                    <div class="field-group">
                        <label for="issue-date">Issue Date/Time:</label>
                        <input type="text" id="issue-date" value="">
                    </div>
                    <div class="field-group">
                        <label for="sender-id">Sender ID:</label>
                        <input type="text" id="sender-id" value="DWCOPS">
                    </div>
                    <div class="field-group">
                        <label for="recipient-id">Recipient ID:</label>
                        <input type="text" id="recipient-id" value="LLWOPS">
                    </div>
                </div>

                <div class="col">
                    <h3>Consignment Details</h3>
                    <div class="field-group">
                        <label for="consignor-name">Consignor Name:</label>
                        <input type="text" id="consignor-name" value="LIMA FREIGHT INTERNATIONAL">
                    </div>
                    <div class="field-group">
                        <label for="consignor-address">Consignor Address:</label>
                        <input type="text" id="consignor-address" value="FREIGHT HOUSE, CARGO WAY">
                    </div>
                    <div class="field-group">
                        <label for="consignor-city">Consignor City:</label>
                        <input type="text" id="consignor-city" value="LONDON">
                    </div>
                    <div class="field-group">
                        <label for="consignor-country">Consignor Country:</label>
                        <input type="text" id="consignor-country" value="UNITED KINGDOM">
                    </div>
                    <div class="field-group">
                        <label for="consignor-country-code">Consignor Country Code:</label>
                        <input type="text" id="consignor-country-code" value="GB">
                    </div>
                    <div class="field-group">
                        <label for="consignor-postcode">Consignor Postcode:</label>
                        <input type="text" id="consignor-postcode" value="W1B 5HQ">
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col">
                    <h3>Consignee Details</h3>
                    <div class="field-group">
                        <label for="consignee-name">Consignee Name:</label>
                        <input type="text" id="consignee-name" value="LIMA FREIGHT AGENTS MALAWI">
                    </div>
                    <div class="field-group">
                        <label for="consignee-address">Consignee Address:</label>
                        <input type="text" id="consignee-address" value="LOGISTICS HUB, INDUSTRIAL AREA">
                    </div>
                    <div class="field-group">
                        <label for="consignee-city">Consignee City:</label>
                        <input type="text" id="consignee-city" value="LILONGWE">
                    </div>
                    <div class="field-group">
                        <label for="consignee-country">Consignee Country:</label>
                        <input type="text" id="consignee-country" value="MALAWI">
                    </div>
                    <div class="field-group">
                        <label for="consignee-country-code">Consignee Country Code:</label>
                        <input type="text" id="consignee-country-code" value="MW">
                    </div>
                </div>

                <div class="col">
                    <h3>Freight Forwarder Details</h3>
                    <div class="field-group">
                        <label for="forwarder-name">Forwarder Name:</label>
                        <input type="text" id="forwarder-name" value="EMIRATES SKYCARGO">
                    </div>
                    <div class="field-group">
                        <label for="forwarder-id">Forwarder Agent ID:</label>
                        <input type="text" id="forwarder-id" value="1234567">
                    </div>
                    <div class="field-group">
                        <label for="forwarder-address">Forwarder Address:</label>
                        <input type="text" id="forwarder-address" value="CARGO MEGA TERMINAL, DUBAI INTERNATIONAL AIRPORT">
                    </div>
                    <div class="field-group">
                        <label for="forwarder-city">Forwarder City:</label>
                        <input type="text" id="forwarder-city" value="DUBAI">
                    </div>
                    <div class="field-group">
                        <label for="forwarder-country">Forwarder Country:</label>
                        <input type="text" id="forwarder-country" value="UNITED ARAB EMIRATES">
                    </div>
                    <div class="field-group">
                        <label for="forwarder-country-code">Forwarder Country Code:</label>
                        <input type="text" id="forwarder-country-code" value="AE">
                    </div>
                    <div class="field-group">
                        <label for="forwarder-postcode">Forwarder Postcode:</label>
                        <input type="text" id="forwarder-postcode" value="DXB1234">
                    </div>
                </div>
            </div>

            <button id="generate-btn">Generate XML</button>
        </div>

        <div class="tab-content" id="output">
            <div class="output-section">
                <label for="xml-output">XML Output:</label>
                <textarea id="xml-output" readonly></textarea>
                <div class="button-group">
                    <button id="copy-btn">Copy to Clipboard</button>
                    <button id="download-btn">Download XML</button>
                </div>
            </div>
        </div>
    </div>

    <script src="js/xfwb.js"></script>
</body>
</html>
