<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IATA XML Generator | Air Cargo Malawi Ltd.</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <style>
        :root {
            --primary-color: #4361ee;
            --secondary-color: #3a0ca3;
            --accent-color: #4cc9f0;
            --dark-color: #212529;
            --light-color: #f8f9fa;
        }

        body {
            font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
            background-color: #f5f7ff;
            color: var(--dark-color);
            line-height: 1.6;
        }

        .container {
            max-width: 1400px;
            margin: 2rem auto;
            background-color: white;
            padding: 2.5rem;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
        }

        .header {
            text-align: center;
            margin-bottom: 2.5rem;
            padding-bottom: 1.5rem;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }

        h1 {
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }

        .lead {
            font-size: 1.1rem;
            color: #6c757d;
            max-width: 700px;
            margin: 0 auto;
        }

        .form-label {
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: var(--dark-color);
        }

        .form-control, .form-select {
            padding: 0.75rem 1rem;
            border-radius: 8px;
            border: 1px solid #dee2e6;
            transition: all 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--accent-color);
            box-shadow: 0 0 0 0.25rem rgba(67, 97, 238, 0.15);
        }

        textarea.form-control {
            min-height: 300px;
            resize: vertical;
        }

        .btn {
            padding: 0.65rem 1.5rem;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-primary:hover {
            background-color: var(--secondary-color);
            border-color: var(--secondary-color);
            transform: translateY(-1px);
        }

        .btn-secondary {
            background-color: #6c757d;
            border-color: #6c757d;
        }

        .btn-secondary:hover {
            background-color: #5a6268;
            border-color: #545b62;
            transform: translateY(-1px);
        }

        .btn-success {
            background-color: #2a9d8f;
            border-color: #2a9d8f;
        }

        .btn-success:hover {
            background-color: #21867a;
            border-color: #1f7d72;
            transform: translateY(-1px);
        }

        .xml-output {
            background-color: #f8fafc;
            padding: 1.25rem;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            max-height: 500px;
            overflow-y: auto;
            color: #334155;
            font-size: 0.9rem;
            line-height: 1.7;
        }

        .xml-output::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        .xml-output::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 10px;
        }

        .xml-output::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 10px;
        }

        .xml-output::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }

        .action-buttons {
            display: flex;
            gap: 0.75rem;
            flex-wrap: wrap;
        }

        .footer {
            text-align: center;
            margin-top: 3rem;
            padding-top: 1.5rem;
            border-top: 1px solid rgba(0, 0, 0, 0.05);
            color: #6c757d;
            font-size: 0.9rem;
        }

        @media (max-width: 768px) {
            .container {
                padding: 1.5rem;
            }

            .header {
                margin-bottom: 1.5rem;
            }

            h1 {
                font-size: 1.8rem;
            }
        }

        /* Animation for the generate button */
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .btn-pulse {
            animation: pulse 2s infinite;
        }

        /* Status indicators */
        .status {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 600;
            display: inline-block;
            margin-bottom: 1rem;
        }

        .status-processing {
            background-color: #fff3bf;
            color: #e67700;
        }

        .status-success {
            background-color: #d3f9d8;
            color: #2b8a3e;
        }

        .status-error {
            background-color: #ffc9c9;
            color: #c92a2a;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="bi bi-file-earmark-code"></i> IATA XML Generator</h1>
            <p class="lead">Easily convert house waybill data from condensed format to fully compliant IATA XML format</p>
            <div class="mt-3">
                <a href="index.html" class="btn btn-sm btn-outline-primary">Flight Manifest (XFFM)</a>
                <a href="xfwb.html" class="btn btn-sm btn-outline-primary me-2">Master Waybill (XFWB)</a>
                <a href="xfzb.html" class="btn btn-sm btn-primary">House Waybill (XFZB)</a>
            </div>
        </div>

        <div class="row g-4">
            <div class="col-lg-6">
                <div class="form-group mb-4">
                    <label for="inputText" class="form-label">House Waybill Data</label>
                    <textarea id="inputText" class="form-control" rows="15" placeholder="Paste your house waybill data here..."></textarea>
                </div>
                <div class="form-group mb-4">
                    <div class="action-buttons">
                        <button id="loadSample" class="btn btn-secondary">
                            <i class="bi bi-file-earmark-text"></i> Load Sample
                        </button>
                        <button id="generateXml" class="btn btn-primary btn-pulse">
                            <i class="bi bi-magic"></i> Generate XML
                        </button>
                        <button id="clearAll" class="btn btn-outline-secondary">
                            <i class="bi bi-trash"></i> Clear
                        </button>
                        <button id="debugBtn" class="btn btn-outline-danger">
                            <i class="bi bi-bug"></i> Debug
                        </button>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div id="statusIndicator" class="status" style="display: none;"></div>
                <div class="form-group mb-4">
                    <label for="xmlOutput" class="form-label">IATA XML Output</label>
                    <div id="xmlOutput" class="xml-output">Your generated XML will appear here...</div>
                </div>
                <div class="form-group">
                    <div class="action-buttons">
                        <button id="downloadXml" class="btn btn-success" disabled>
                            <i class="bi bi-download"></i> Download XML
                        </button>
                        <button id="copyXml" class="btn btn-outline-primary" disabled>
                            <i class="bi bi-clipboard"></i> Copy to Clipboard
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="footer">
            <p>© 2025 Air Cargo Malawi Ltd. | IATA XML Generator</p>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // API base URL - use the current origin
            const API_BASE_URL = window.location.origin;

            const inputText = document.getElementById('inputText');
            const xmlOutput = document.getElementById('xmlOutput');
            const generateXmlBtn = document.getElementById('generateXml');
            const loadSampleBtn = document.getElementById('loadSample');
            const downloadXmlBtn = document.getElementById('downloadXml');
            const copyXmlBtn = document.getElementById('copyXml');
            const clearAllBtn = document.getElementById('clearAll');
            const statusIndicator = document.getElementById('statusIndicator');


            // Load sample data
            loadSampleBtn.addEventListener('click', function() {
                fetch(`${API_BASE_URL}/api/sample-xfzb`)
                    .then(response => {
                        if (!response.ok) throw new Error('Network response was not ok');
                        return response.json();
                    })
                    .then(data => {
                        if (data.success && data.sample) {
                            inputText.value = data.sample;
                            showStatus('Sample data loaded successfully', 'success');
                        } else {
                            throw new Error(data.error || 'Invalid sample data');
                        }
                    })
                    .catch(error => {
                        console.error('Error loading sample:', error);
                        // Fallback to local sample data if API fails
                        inputText.value = "FFM/8\n1/KQ756/28APR/NBO/5Y-FFE\nLLW\n706-51634332DXBLLW/P3K111.0MC0.26T13/LAPTOPS LITHIUM\n/ELI/GEN\n706-60497953BOMLLW/T2K55.0MC0.33/COURIER MATERIA\n/COU\n706-60606280NBOLLW/T2K2.0MC0.01/DHL EXPRESS\n/COU\n706-41026672DXBLLW/T1K7.0MC0.13/COURIER\n/COU\nAPL/NIL";
                        showStatus('Using local sample data', 'success');
                    });

            });

            // Generate XML
            generateXmlBtn.addEventListener('click', function() {
                const text = inputText.value.trim();
                if (!text) {
                    showStatus('Please enter some text or load a sample', 'error');
                    return;
                }

                xmlOutput.textContent = '';
                showStatus('Generating XML...', 'processing');
                downloadXmlBtn.disabled = true;
                copyXmlBtn.disabled = true;

                fetch(`${API_BASE_URL}/api/generate-xfzb`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ input_text: text })
                })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // Handle single file response
                            if (data.xml) {
                                xmlOutput.textContent = formatXml(data.xml);
                                downloadXmlBtn.disabled = false;
                                copyXmlBtn.disabled = false;
                                showStatus('XML generated successfully', 'success');
                            }
                            // Handle multiple files response
                            else if (data.files && data.multiple_files) {
                                displayMultipleFiles(data.files);
                                downloadXmlBtn.disabled = false;
                                copyXmlBtn.disabled = true; // Disable copy for multiple files
                                showStatus(`${data.file_count} XML files generated successfully`, 'success');
                            }
                            else {
                                throw new Error('Invalid response format');
                            }
                        } else {
                            throw new Error(data.error || 'Unknown error');
                        }
                    })
                    .catch(error => {
                        console.error('Error generating XML:', error);
                        // Show detailed error information
                        const errorMessage = `Error: ${error.message}\nAPI URL: ${API_BASE_URL}/api/generate-xfzb`;
                        showStatus(errorMessage, 'error');
                        xmlOutput.textContent = errorMessage;
                    });
            });

            // Download XML
            downloadXmlBtn.addEventListener('click', function() {
                // Check if we have multiple files
                if (window.generatedFiles && window.generatedFiles.length > 1) {
                    // Download multiple files
                    window.generatedFiles.forEach(file => {
                        const blob = new Blob([file.xml], { type: 'application/xml' });
                        const url = URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = file.filename;
                        document.body.appendChild(a);
                        a.click();
                        document.body.removeChild(a);
                        URL.revokeObjectURL(url);
                    });
                    showStatus(`${window.generatedFiles.length} XML files downloaded`, 'success');
                } else {
                    // Download single file
                    const xml = xmlOutput.textContent;
                    if (xml && !xml.includes('Your generated XML will appear here')) {
                        const blob = new Blob([xml], { type: 'application/xml' });
                        const url = URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = `house_waybill_${new Date().toISOString().slice(0,10)}.xml`;
                        document.body.appendChild(a);
                        a.click();
                        document.body.removeChild(a);
                        URL.revokeObjectURL(url);
                        showStatus('XML file downloaded', 'success');
                    }
                }
            });

            // Copy XML to clipboard
            copyXmlBtn.addEventListener('click', function() {
                const xml = xmlOutput.textContent;
                if (xml && !xml.includes('Your generated XML will appear here')) {
                    navigator.clipboard.writeText(xml)
                        .then(() => {
                            showStatus('XML copied to clipboard', 'success');
                            // Change icon temporarily to indicate success
                            const icon = copyXmlBtn.querySelector('i');
                            icon.classList.remove('bi-clipboard');
                            icon.classList.add('bi-check2');
                            setTimeout(() => {
                                icon.classList.remove('bi-check2');
                                icon.classList.add('bi-clipboard');
                            }, 2000);
                        })
                        .catch(err => {
                            console.error('Failed to copy: ', err);
                            showStatus('Failed to copy to clipboard', 'error');
                        });
                }
            });

            // Clear all fields
            clearAllBtn.addEventListener('click', function() {
                inputText.value = '';
                xmlOutput.textContent = 'Your generated XML will appear here...';
                downloadXmlBtn.disabled = true;
                copyXmlBtn.disabled = true;
                statusIndicator.style.display = 'none';
                // Clear stored files
                window.generatedFiles = null;
            });

            // Debug button
            const debugBtn = document.getElementById('debugBtn');
            debugBtn.addEventListener('click', function() {
                // Test API endpoints
                const debugInfo = {
                    apiBaseUrl: API_BASE_URL,
                    userAgent: navigator.userAgent,
                    timestamp: new Date().toISOString()
                };

                // Test sample endpoint
                fetch(`${API_BASE_URL}/api/sample-xfzb`)
                    .then(response => {
                        debugInfo.sampleEndpoint = {
                            status: response.status,
                            ok: response.ok
                        };
                        return response.json();
                    })
                    .then(data => {
                        debugInfo.sampleEndpoint.data = data;
                    })
                    .catch(error => {
                        debugInfo.sampleEndpoint.error = error.message;
                    })
                    .finally(() => {
                        // Display debug info
                        xmlOutput.textContent = JSON.stringify(debugInfo, null, 2);
                        showStatus('Debug information displayed', 'info');
                    });
            });

            // Display multiple XML files
            function displayMultipleFiles(files) {
                let output = `Generated ${files.length} XML files:\n\n`;

                files.forEach((file, index) => {
                    output += `=== File ${index + 1}: ${file.filename} ===\n`;
                    output += formatXml(file.xml);
                    output += '\n\n';
                });

                xmlOutput.textContent = output;

                // Store files for download
                window.generatedFiles = files;
            }

            // Format XML for display
            function formatXml(xml) {
                // Simple formatting - in a real app you might want a more robust solution
                return xml
                    .replace(/></g, '>\n<')
                    .replace(/^\s+/gm, '')
                    .replace(/\n+/g, '\n');
            }

            // Show status message
            function showStatus(message, type) {
                statusIndicator.textContent = message;
                statusIndicator.className = 'status status-' + type;
                statusIndicator.style.display = 'block';

                // Auto-hide after 5 seconds
                setTimeout(() => {
                    statusIndicator.style.display = 'none';
                }, 5000);
            }
        });
    </script>
</body>
</html>
