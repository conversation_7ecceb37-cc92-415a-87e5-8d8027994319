<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>XFZB XML Generator - House Waybill</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }

        .card {
            border: none;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            border-radius: 0.5rem;
        }

        .card-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            font-weight: 600;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
            transform: translateY(-1px);
        }

        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .xml-output {
            background-color: #2d3748;
            border-radius: 0.5rem;
            max-height: 600px;
            overflow-y: auto;
        }

        .xml-output pre {
            margin: 0;
            background: transparent !important;
        }

        .empty-output {
            background-color: #f8f9fa;
            border: 2px dashed #dee2e6;
            border-radius: 0.5rem;
            padding: 3rem;
            text-align: center;
            color: #6c757d;
        }

        .tabs {
            display: flex;
            border-bottom: 2px solid #dee2e6;
            margin-bottom: 1rem;
        }

        .tab {
            padding: 0.75rem 1.5rem;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-bottom: none;
            cursor: pointer;
            margin-right: 0.25rem;
            border-radius: 0.5rem 0.5rem 0 0;
            transition: all 0.3s ease;
        }

        .tab:hover {
            background-color: #e9ecef;
        }

        .tab.active {
            background-color: white;
            border-bottom: 2px solid white;
            margin-bottom: -2px;
            font-weight: 600;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .input-section {
            margin-bottom: 2rem;
        }

        .button-group {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
            margin-top: 1rem;
        }

        .field-group {
            margin-top: 1rem;
            padding: 1rem;
            background-color: #f8f9fa;
            border-radius: 0.5rem;
            border: 1px solid #dee2e6;
        }

        .field-row {
            display: flex;
            gap: 1rem;
            margin-bottom: 1rem;
            align-items: center;
        }

        .field-row label {
            min-width: 120px;
            font-weight: 600;
            margin-bottom: 0;
        }

        .field-row input, .field-row select {
            flex: 1;
        }

        .status-message {
            padding: 0.75rem 1rem;
            border-radius: 0.5rem;
            margin-bottom: 1rem;
            font-weight: 500;
        }

        .status-processing {
            background-color: #fff3bf;
            color: #e67700;
        }

        .status-success {
            background-color: #d3f9d8;
            color: #2b8a3e;
        }

        .status-error {
            background-color: #ffc9c9;
            color: #c92a2a;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="bi bi-file-earmark-code"></i> IATA XML Generator</h1>
            <p class="lead">Easily convert house waybill data from condensed format to fully compliant IATA XML format</p>
            <div class="mt-3">
                <a href="index.html" class="btn btn-sm btn-outline-light me-2">Flight Manifest (XFFM)</a>
                <a href="xfwb.html" class="btn btn-sm btn-outline-light me-2">Master Waybill (XFWB)</a>
                <a href="xfzb.html" class="btn btn-sm btn-light">House Waybill (XFZB)</a>
            </div>
        </div>

        <div class="tabs">
            <div class="tab active" data-tab="input">Input Data</div>
            <div class="tab" data-tab="config">Configuration</div>
            <div class="tab" data-tab="output">XML Output</div>
        </div>

        <div id="status-container"></div>

        <div class="tab-content active" id="input">
            <div class="input-section">
                <label for="xfzb-input">Paste XFZB Data:</label>
                <textarea id="xfzb-input" class="form-control" rows="10" placeholder="Enter XFZB data (e.g., SHAS51282599/T1K4.0MC0.027/panel pc)"></textarea>
                <div class="button-group">
                    <button id="parse-btn" class="btn btn-secondary">Parse Data</button>
                    <button id="load-sample-btn" class="btn btn-outline-secondary">Load Sample</button>
                    <button id="clear-btn" class="btn btn-outline-danger">Clear</button>
                </div>
                <div id="parse-result" class="field-group"></div>
            </div>
        </div>

        <div class="tab-content" id="config">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Configuration Options</h5>
                </div>
                <div class="card-body">
                    <div class="field-row">
                        <label for="master-awb">Master AWB:</label>
                        <input type="text" id="master-awb" class="form-control" value="724-42499866">
                    </div>
                    <div class="field-row">
                        <label for="consignor-name">Consignor Name:</label>
                        <input type="text" id="consignor-name" class="form-control" value="IEI INTEGRATION CORP">
                    </div>
                    <div class="field-row">
                        <label for="consignee-name">Consignee Name:</label>
                        <input type="text" id="consignee-name" class="form-control" value="CAPTEC LIMITED">
                    </div>
                    <div class="field-row">
                        <label for="forwarder-name">Freight Forwarder:</label>
                        <input type="text" id="forwarder-name" class="form-control" value="KUEHNE + NAGEL LIMITED">
                    </div>
                </div>
            </div>
        </div>

        <div class="tab-content" id="output">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h4>Generated XML Output</h4>
                <div>
                    <button id="generate-xml-btn" class="btn btn-primary me-2">
                        <i class="bi bi-gear-fill"></i> Generate XML
                    </button>
                    <button id="download-xml-btn" class="btn btn-success me-2" disabled>
                        <i class="bi bi-download"></i> Download
                    </button>
                    <button id="copy-xml-btn" class="btn btn-outline-secondary" disabled>
                        <i class="bi bi-clipboard"></i> Copy
                    </button>
                </div>
            </div>
            <div class="xml-output">
                <pre><code id="xml-output" class="language-xml"></code></pre>
                <div id="empty-output" class="empty-output">
                    <i class="bi bi-file-earmark-code" style="font-size: 3rem; opacity: 0.3;"></i>
                    <p class="mt-3 mb-0">XML output will appear here after generation</p>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
    <script>
        // Configuration
        const API_BASE_URL = 'http://localhost:5001';

        // DOM elements
        const xfzbInput = document.getElementById('xfzb-input');
        const parseBtn = document.getElementById('parse-btn');
        const loadSampleBtn = document.getElementById('load-sample-btn');
        const clearBtn = document.getElementById('clear-btn');
        const parseResult = document.getElementById('parse-result');
        const generateXmlBtn = document.getElementById('generate-xml-btn');
        const downloadXmlBtn = document.getElementById('download-xml-btn');
        const copyXmlBtn = document.getElementById('copy-xml-btn');
        const xmlOutput = document.getElementById('xml-output');
        const emptyOutput = document.getElementById('empty-output');
        const statusContainer = document.getElementById('status-container');

        // Configuration fields
        const masterAwbField = document.getElementById('master-awb');
        const consignorNameField = document.getElementById('consignor-name');
        const consigneeNameField = document.getElementById('consignee-name');
        const forwarderNameField = document.getElementById('forwarder-name');

        // Tab functionality
        document.querySelectorAll('.tab').forEach(tab => {
            tab.addEventListener('click', function() {
                const targetTab = this.getAttribute('data-tab');
                
                // Remove active class from all tabs and content
                document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
                document.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));
                
                // Add active class to clicked tab and corresponding content
                this.classList.add('active');
                document.getElementById(targetTab).classList.add('active');
            });
        });

        // Status message function
        function showStatus(message, type) {
            statusContainer.innerHTML = `<div class="status-message status-${type}">${message}</div>`;
            setTimeout(() => {
                statusContainer.innerHTML = '';
            }, 5000);
        }

        // Load sample data
        loadSampleBtn.addEventListener('click', function() {
            fetch(`${API_BASE_URL}/api/sample-xfzb`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        xfzbInput.value = data.sample;
                        showStatus('Sample XFZB data loaded successfully', 'success');
                    } else {
                        showStatus('Failed to load sample data: ' + data.error, 'error');
                    }
                })
                .catch(error => {
                    showStatus('Failed to load sample data: ' + error.message, 'error');
                });
        });

        // Clear input
        clearBtn.addEventListener('click', function() {
            xfzbInput.value = '';
            parseResult.innerHTML = '';
            xmlOutput.textContent = '';
            emptyOutput.style.display = 'block';
            downloadXmlBtn.disabled = true;
            copyXmlBtn.disabled = true;
            showStatus('Input cleared', 'success');
        });

        // Parse XFZB data
        parseBtn.addEventListener('click', function() {
            const text = xfzbInput.value.trim();
            if (!text) {
                showStatus('Please enter some XFZB data first', 'error');
                return;
            }

            // Simple parsing for display
            const lines = text.split('\n');
            let parseHtml = '<h6>Parsed House AWBs:</h6>';
            
            lines.forEach((line, index) => {
                if (line.trim() && !line.startsWith('/')) {
                    const match = line.match(/([A-Z0-9]+)\/([ST])(\d+)K(\d+\.\d+)MC(\d+\.\d+)\/(.*)/);
                    if (match) {
                        parseHtml += `
                            <div class="mb-2 p-2 border rounded">
                                <strong>House AWB ${index + 1}:</strong> ${match[1]}<br>
                                <small>Pieces: ${match[3]}, Weight: ${match[4]}kg, Volume: ${match[5]}m³</small><br>
                                <small>Description: ${match[6]}</small>
                            </div>
                        `;
                    }
                }
            });

            parseResult.innerHTML = parseHtml;
            showStatus('XFZB data parsed successfully', 'success');
        });

        // Generate XML
        generateXmlBtn.addEventListener('click', function() {
            const text = xfzbInput.value.trim();
            if (!text) {
                showStatus('Please enter some text or load a sample', 'error');
                return;
            }

            xmlOutput.textContent = '';
            showStatus('Generating XML...', 'processing');
            downloadXmlBtn.disabled = true;
            copyXmlBtn.disabled = true;

            // Prepare configuration
            const config = {
                master_awb_number: masterAwbField.value,
                consignor_name: consignorNameField.value,
                consignee_name: consigneeNameField.value,
                freight_forwarder_name: forwarderNameField.value
            };

            fetch(`${API_BASE_URL}/api/generate-xfzb`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ 
                    input_text: text,
                    config: config
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    xmlOutput.textContent = data.xml;
                    emptyOutput.style.display = 'none';
                    downloadXmlBtn.disabled = false;
                    copyXmlBtn.disabled = false;
                    
                    // Highlight syntax
                    Prism.highlightElement(xmlOutput);
                    
                    showStatus('XFZB XML generated successfully!', 'success');
                    
                    // Switch to output tab
                    document.querySelector('.tab[data-tab="output"]').click();
                } else {
                    showStatus('Error generating XML: ' + data.error, 'error');
                }
            })
            .catch(error => {
                showStatus('Error generating XML: ' + error.message, 'error');
            });
        });

        // Download XML
        downloadXmlBtn.addEventListener('click', function() {
            const xmlContent = xmlOutput.textContent;
            if (!xmlContent) return;

            const blob = new Blob([xmlContent], { type: 'application/xml' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'house_waybill.xml';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            showStatus('XML downloaded successfully', 'success');
        });

        // Copy XML
        copyXmlBtn.addEventListener('click', function() {
            const xmlContent = xmlOutput.textContent;
            if (!xmlContent) return;

            navigator.clipboard.writeText(xmlContent).then(() => {
                showStatus('XML copied to clipboard', 'success');
            }).catch(() => {
                showStatus('Failed to copy XML to clipboard', 'error');
            });
        });

        // Initialize
        emptyOutput.style.display = 'block';
    </script>
</body>
</html>
