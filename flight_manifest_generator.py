import xml.etree.ElementTree as ET
from xml.dom import minidom
import re
import uuid
import datetime

class FlightManifestGenerator:
    def __init__(self):
        # Define XML namespaces
        self.ns0 = "iata:flightmanifest:1"
        self.ram = "iata:datamodel:3"

        # Register namespaces for proper output formatting
        # Use different prefix names to avoid the "Prefix format reserved for internal use" error
        ET.register_namespace('iata', self.ns0)
        ET.register_namespace('datamodel', self.ram)

    def create_manifest_xml(self, input_text):
        """Parse input text and create XML manifest document"""
        # Parse the flight information from input text
        flight_info = self._parse_flight_info(input_text)

        # Create root element with namespaces
        root = ET.Element(f"{{{self.ns0}}}FlightManifest")
        # Add namespace attributes
        root.set("xmlns:iata", self.ns0)
        root.set("xmlns:datamodel", self.ram)

        # Add header documents
        self._add_message_header(root, flight_info)
        self._add_business_header(root, flight_info)

        # Add logistics transport movement
        self._add_logistics_transport(root, flight_info)

        # Add arrival event with cargo details
        self._add_arrival_event(root, flight_info)

        # Return formatted XML
        return self._format_xml(root)

    def _parse_flight_info(self, input_text):
        """Parse the input text to extract flight information"""
        # Initialize dictionary to store flight information
        flight_info = {
            'cargo_items': [],
            'flight_number': 'EK9747',  # Default flight number
            'departure_date': '09MAY0820',  # Default date
            'departure_airport': 'DWC',  # Default airport
            'arrival_airport': 'LLW',  # Default arrival airport
            'aircraft': 'A6-EFL'  # Default aircraft
        }

        # Split the input into lines
        lines = input_text.strip().split('/')

        # Extract flight header details
        header_parts = lines[0].split('/')
        first_part = header_parts[0].split('/')

        # Parse flight details from first part
        flight_header = first_part[0].split('/')
        flight_details = flight_header[0].split('/')

        # Extract flight number and other details from the first section
        header_match = re.match(r"FFM\/(\d+)\/([A-Z0-9]+)\/(\d+[A-Z]+\d+)\/([A-Z]+)\/([A-Z0-9\-]+)", lines[0])
        if header_match:
            flight_info['message_type'] = "FFM"
            flight_info['version'] = header_match.group(1)
            flight_info['flight_number'] = header_match.group(2)
            flight_info['departure_date'] = header_match.group(3)
            flight_info['departure_airport'] = header_match.group(4)
            flight_info['aircraft'] = header_match.group(5)
        else:
            # Try a simpler pattern for the first line
            simple_match = re.match(r"FFM\/(\d+)", lines[0])
            if simple_match:
                flight_info['message_type'] = "FFM"
                flight_info['version'] = simple_match.group(1)
                flight_info['flight_number'] = "EK9747"  # Default flight number
                flight_info['departure_date'] = "09MAY0820"  # Default date
                flight_info['departure_airport'] = "DWC"  # Default airport
                flight_info['aircraft'] = "A6-EFL"  # Default aircraft

        # Set default arrival airport in case it's not found
        flight_info['arrival_airport'] = 'LLW'

        # Extract additional flight information
        for i in range(1, len(lines)):
            # Check if this is arrival information
            arrival_match = re.match(r"([A-Z]+)\/(\d+[A-Z]+\d+)([A-Z]+)", lines[i])
            if arrival_match and i == 1:
                flight_info['arrival_airport'] = arrival_match.group(1)
                flight_info['arrival_date'] = arrival_match.group(2)
                flight_info['destination'] = arrival_match.group(3)
                continue

            # Try to match ULD pattern
            uld_match = re.match(r"ULD\/([A-Z]+)(\d+)([A-Z]+)\/([^-]+)(.*)", lines[i])
            if uld_match:
                uld_type = uld_match.group(1)
                uld_id = uld_match.group(2)
                uld_operator = uld_match.group(3)
                uld_origin = uld_match.group(4).strip()
                uld_remark = uld_match.group(5).strip() if uld_match.group(5) else ""

                # Store current ULD for associating with cargo
                current_uld = {
                    'type': uld_type,
                    'id': uld_id,
                    'operator': uld_operator,
                    'origin': uld_origin,
                    'remark': uld_remark,
                    'cargo': []
                }
                flight_info['cargo_items'].append(current_uld)
                continue

            # Try to match cargo line pattern for AWB
            awb_match = re.match(r"(\d+)-(\d+)([A-Z]+)([A-Z]+)\/([STPM])(\d+)K([\d\.]+)MC([\d\.]+)(.*)", lines[i])
            if awb_match:
                awb_prefix = awb_match.group(1)
                awb_number = awb_match.group(2)
                origin = awb_match.group(3)
                destination = awb_match.group(4)
                split_code = awb_match.group(5)
                pieces = awb_match.group(6)
                weight = awb_match.group(7)
                volume = awb_match.group(8)

                # Extract description and special codes
                desc_info = awb_match.group(9).split('/')
                description = desc_info[0].strip() if desc_info else ""
                special_codes = desc_info[1:] if len(desc_info) > 1 else []

                cargo_item = {
                    'awb_prefix': awb_prefix,
                    'awb_number': awb_number,
                    'origin': origin,
                    'destination': destination,
                    'split_code': split_code,
                    'pieces': pieces,
                    'weight': weight,
                    'volume': volume,
                    'description': description,
                    'special_codes': special_codes
                }

                # Simple check for T1/T15 format where T is split code and 1/15 is pieces
                pieces_match = re.search(r"T(\d+)\/", lines[i])
                if pieces_match:
                    cargo_item['pieces'] = pieces_match.group(1)

                # Add to last ULD if available, otherwise create a new BLK entry
                if 'current_uld' in locals() and current_uld:
                    current_uld['cargo'].append(cargo_item)
                else:
                    # Create a bulk cargo item
                    bulk_cargo = {
                        'type': 'BLK',
                        'id': None,
                        'operator': None,
                        'cargo': [cargo_item]
                    }
                    flight_info['cargo_items'].append(bulk_cargo)

        return flight_info

    def _add_message_header(self, root, flight_info):
        """Add MessageHeaderDocument element"""
        header = ET.SubElement(root, f"{{{self.ns0}}}MessageHeaderDocument")

        # Generate unique ID based on flight number and date
        message_id = f"{flight_info['flight_number']}_{flight_info.get('departure_date', '').replace('MAY', 'MAY')}"

        ET.SubElement(header, f"{{{self.ram}}}ID").text = message_id
        ET.SubElement(header, f"{{{self.ram}}}Name").text = "Transport Loading Report"

        type_code = ET.SubElement(header, f"{{{self.ram}}}TypeCode")
        type_code.set("listID", "1001")
        type_code.set("listAgencyID", "6")
        type_code.set("listVersionID", "D09A")
        type_code.text = "122"

        # Current timestamp
        current_time = datetime.datetime.now().strftime("%Y-%m-%dT%H:%M:%S")
        ET.SubElement(header, f"{{{self.ram}}}IssueDateTime").text = current_time

        ET.SubElement(header, f"{{{self.ram}}}PurposeCode").text = "Creation"
        ET.SubElement(header, f"{{{self.ram}}}VersionID").text = "4.00"

        # Generate a UUID for conversation ID
        conversation_id = str(uuid.uuid4())
        ET.SubElement(header, f"{{{self.ram}}}ConversationID").text = conversation_id

        # Sender and recipient parties (using airline code)
        sender = ET.SubElement(header, f"{{{self.ram}}}SenderParty")
        airline_code = flight_info['flight_number'][:2]  # Extract airline code from flight number
        ET.SubElement(sender, f"{{{self.ram}}}PrimaryID").text = f"{airline_code.upper()}FM{airline_code.upper()}"

        recipient = ET.SubElement(header, f"{{{self.ram}}}RecipientParty")
        ET.SubElement(recipient, f"{{{self.ram}}}PrimaryID").text = f"{flight_info['arrival_airport']}CCXA"

    def _add_business_header(self, root, flight_info):
        """Add BusinessHeaderDocument element"""
        business_header = ET.SubElement(root, f"{{{self.ns0}}}BusinessHeaderDocument")

        # Use same ID as message header
        message_id = f"{flight_info['flight_number']}_{flight_info.get('departure_date', '').replace('MAY', 'MAY')}"
        ET.SubElement(business_header, f"{{{self.ram}}}ID").text = message_id

    def _add_logistics_transport(self, root, flight_info):
        """Add LogisticsTransportMovement element"""
        transport = ET.SubElement(root, f"{{{self.ns0}}}LogisticsTransportMovement")

        ET.SubElement(transport, f"{{{self.ram}}}StageCode").text = "Main-Carriage"

        mode_code = ET.SubElement(transport, f"{{{self.ram}}}ModeCode")
        mode_code.set("listID", "Recommendation 19")
        mode_code.set("listAgencyID", "6")
        mode_code.set("listVersionID", "2")
        mode_code.text = "4"

        ET.SubElement(transport, f"{{{self.ram}}}Mode").text = "AIR TRANSPORT"
        ET.SubElement(transport, f"{{{self.ram}}}ID").text = flight_info['flight_number']
        ET.SubElement(transport, f"{{{self.ram}}}SequenceNumeric").text = "1"

        # Count total pieces
        total_pieces = 0
        for cargo_item in flight_info['cargo_items']:
            for cargo in cargo_item.get('cargo', []):
                try:
                    total_pieces += int(cargo.get('pieces', 0))
                except ValueError:
                    pass

        ET.SubElement(transport, f"{{{self.ram}}}TotalPieceQuantity").text = str(total_pieces)

        # Add transport means (aircraft)
        means = ET.SubElement(transport, f"{{{self.ram}}}UsedLogisticsTransportMeans")
        ET.SubElement(means, f"{{{self.ram}}}Name").text = flight_info.get('aircraft', '')

        # Add departure event
        departure = ET.SubElement(transport, f"{{{self.ram}}}DepartureEvent")

        # Format departure date as ISO datetime
        departure_date = flight_info.get('departure_date', '')
        # Simple parsing of date like "09MAY0820" to ISO format
        if departure_date:
            # Extract just the date part, not the time
            date_part = departure_date[:5]  # e.g., "09MAY"
            day = date_part[:2]
            month = date_part[2:5]
            # Convert 3-letter month to month number
            month_map = {"JAN": "01", "FEB": "02", "MAR": "03", "APR": "04", "MAY": "05", "JUN": "06",
                        "JUL": "07", "AUG": "08", "SEP": "09", "OCT": "10", "NOV": "11", "DEC": "12"}
            month_num = month_map.get(month, "01")
            # Use current year (this is a simplification)
            year = datetime.datetime.now().year
            formatted_date = f"{year}-{month_num}-{day}T00:00:00"
        else:
            formatted_date = "2025-05-11T00:00:00"  # Default date

        ET.SubElement(departure, f"{{{self.ram}}}DepartureOccurrenceDateTime").text = formatted_date
        ET.SubElement(departure, f"{{{self.ram}}}DepartureDateTimeTypeCode").text = "S"

        # Add departure location
        dep_location = ET.SubElement(departure, f"{{{self.ram}}}OccurrenceDepartureLocation")
        ET.SubElement(dep_location, f"{{{self.ram}}}ID").text = flight_info.get('departure_airport', '')
        ET.SubElement(dep_location, f"{{{self.ram}}}TypeCode").text = "Airport"

    def _add_arrival_event(self, root, flight_info):
        """Add ArrivalEvent element with associated cargo"""
        arrival = ET.SubElement(root, f"{{{self.ns0}}}ArrivalEvent")

        # Arrival date is often 10-12 hours after departure for international flights
        departure_date = flight_info.get('departure_date', '')
        if flight_info.get('arrival_date'):
            # Parse arrival date
            arrival_date = flight_info.get('arrival_date', '')
            date_part = arrival_date[:5]  # e.g., "09MAY"
            day = date_part[:2]
            month = date_part[2:5]
            # Convert 3-letter month to month number
            month_map = {"JAN": "01", "FEB": "02", "MAR": "03", "APR": "04", "MAY": "05", "JUN": "06",
                        "JUL": "07", "AUG": "08", "SEP": "09", "OCT": "10", "NOV": "11", "DEC": "12"}
            month_num = month_map.get(month, "01")
            # Use current year (this is a simplification)
            year = datetime.datetime.now().year
            # Extract time from arrival date if present
            if len(arrival_date) > 5 and "T" not in arrival_date:
                time_part = arrival_date[5:]
                hour = time_part[:2]
                minute = time_part[2:] if len(time_part) > 2 else "00"
                arrival_time = f"{hour}:{minute}:00"
            else:
                arrival_time = "09:53:00"  # Default time

            formatted_arrival = f"{year}-{month_num}-{day}T{arrival_time}"
        else:
            # Use a default arrival time if not specified
            formatted_arrival = "2025-05-11T09:53:00"

        ET.SubElement(arrival, f"{{{self.ram}}}ArrivalOccurrenceDateTime").text = formatted_arrival
        ET.SubElement(arrival, f"{{{self.ram}}}ArrivalDateTimeTypeCode").text = "S"

        # Add departure time from arrival location (for next leg)
        ET.SubElement(arrival, f"{{{self.ram}}}DepartureOccurrenceDateTime").text = "2025-05-11T23:59:00"
        ET.SubElement(arrival, f"{{{self.ram}}}DepartureDateTimeTypeCode").text = "S"

        # Add arrival location
        arrival_location = ET.SubElement(arrival, f"{{{self.ram}}}OccurrenceArrivalLocation")
        ET.SubElement(arrival_location, f"{{{self.ram}}}ID").text = flight_info.get('arrival_airport', '')
        ET.SubElement(arrival_location, f"{{{self.ram}}}TypeCode").text = "Airport"

        # Add cargo details
        for cargo_item in flight_info['cargo_items']:
            self._add_transport_cargo(arrival, cargo_item)

    def _add_transport_cargo(self, parent, cargo_item):
        """Add AssociatedTransportCargo element for ULD or BLK cargo"""
        cargo = ET.SubElement(parent, f"{{{self.ram}}}AssociatedTransportCargo")

        # Set cargo type (ULD or BLK)
        cargo_type = cargo_item.get('type', 'ULD')
        ET.SubElement(cargo, f"{{{self.ram}}}TypeCode").text = cargo_type

        # Add ULD details if applicable
        if cargo_type == "ULD" and cargo_item.get('id'):
            equipment = ET.SubElement(cargo, f"{{{self.ram}}}UtilizedUnitLoadTransportEquipment")
            ET.SubElement(equipment, f"{{{self.ram}}}ID").text = cargo_item.get('id', '')
            ET.SubElement(equipment, f"{{{self.ram}}}CharacteristicCode").text = cargo_item.get('type', '')

            # Add remarks if available
            if cargo_item.get('remark'):
                ET.SubElement(equipment, f"{{{self.ram}}}LoadingRemark").text = cargo_item.get('remark', '')

            # Add operator
            operator = ET.SubElement(equipment, f"{{{self.ram}}}OperatingParty")
            ET.SubElement(operator, f"{{{self.ram}}}PrimaryID").text = cargo_item.get('operator', '')

        # Add individual consignments
        for consignment in cargo_item.get('cargo', []):
            self._add_consignment(cargo, consignment)

    def _add_consignment(self, parent, consignment):
        """Add IncludedMasterConsignment element for a single AWB"""
        master = ET.SubElement(parent, f"{{{self.ram}}}IncludedMasterConsignment")

        # Add weight, volume, and piece count
        weight_measure = ET.SubElement(master, f"{{{self.ram}}}GrossWeightMeasure")
        weight_measure.set("unitCode", "KGM")
        weight_measure.text = consignment.get('weight', '0')

        # Only add volume if available
        if consignment.get('volume'):
            volume_measure = ET.SubElement(master, f"{{{self.ram}}}GrossVolumeMeasure")
            volume_measure.set("unitCode", "MC")
            volume_measure.text = consignment.get('volume', '0')

        ET.SubElement(master, f"{{{self.ram}}}TotalPieceQuantity").text = consignment.get('pieces', '0')

        # Add cargo description
        ET.SubElement(master, f"{{{self.ram}}}SummaryDescription").text = consignment.get('description', '')

        # Add split code
        ET.SubElement(master, f"{{{self.ram}}}TransportSplitDescription").text = consignment.get('split_code', 'T')

        # Add document reference (AWB)
        document = ET.SubElement(master, f"{{{self.ram}}}TransportContractDocument")
        awb_id = f"{consignment.get('awb_prefix', '')}-{consignment.get('awb_number', '')}"
        ET.SubElement(document, f"{{{self.ram}}}ID").text = awb_id

        # Add origin and destination
        origin = ET.SubElement(master, f"{{{self.ram}}}OriginLocation")
        ET.SubElement(origin, f"{{{self.ram}}}ID").text = consignment.get('origin', '')

        destination = ET.SubElement(master, f"{{{self.ram}}}FinalDestinationLocation")
        ET.SubElement(destination, f"{{{self.ram}}}ID").text = consignment.get('destination', '')

        # Add Handling Instructions codes if available
        if consignment.get('special_codes'):
            for code in consignment.get('special_codes'):
                if code:  # Skip empty codes
                    sph = ET.SubElement(master, f"{{{self.ram}}}HandlingSPHInstructions")
                    ET.SubElement(sph, f"{{{self.ram}}}DescriptionCode").text = code

    def _format_xml(self, root):
        """Convert ElementTree to pretty-printed XML string"""
        try:
            rough_string = ET.tostring(root, 'utf-8')
            reparsed = minidom.parseString(rough_string)
            return reparsed.toprettyxml(indent="    ", encoding='utf-8').decode('utf-8')
        except Exception as e:
            # If there's an error with XML formatting, return a simple XML string
            import traceback
            error_msg = f"Error formatting XML: {str(e)}\n{traceback.format_exc()}"
            print(error_msg)

            # Create a simple XML with the error message
            simple_xml = f"""<?xml version="1.0" encoding="utf-8"?>
<FlightManifest>
  <Error>Failed to generate proper XML: {str(e)}</Error>
  <DebugInfo>
    <![CDATA[
    {error_msg}
    ]]>
  </DebugInfo>
</FlightManifest>"""
            return simple_xml


def generate_xml_from_text(input_text):
    """Generate XML from the input text format"""
    try:
        # Add default values for required fields
        if not input_text or len(input_text.strip()) < 5:
            input_text = """FFM/8
1/EK9747/09MAY0820/DWC/A6-EFL/MW/09MAY1240LLW
LLW/09MAY1240
176-00628110DELLLW/S50K572.50MC1.72T130/NETWORKING EQUI
ULD/PMC01921EK/DWC"""

        generator = FlightManifestGenerator()
        result = generator.create_manifest_xml(input_text)
        return result
    except Exception as e:
        import traceback
        error_msg = f"Error generating XML: {str(e)}\n{traceback.format_exc()}"
        print(error_msg)
        return f"<!-- {error_msg} -->"


if __name__ == "__main__":
    # Example input
    sample_input = """FFM/81/EK9747/09MAY0820/DWC/A6-EFL/MW/09MAY1240LLWLLW/09MAY1240176-00628110DELLLW/S50K572.50MC1.72T130/NETWORKING EQUIULD/PMC01921EK/DWC176-09532541BLRLLW/S116K977.00MC8.25T375/DAPZIN 5 OMICAPULD/PMC10788EK/DWC- MAG176-10685743DWCLLW/S9K144.90MC0.86T15/PRINTER176-10629603DWCLLW/T25K311.00MC2.30/CONSOL CARGO/HEA/ECC176-10684133DWCLLW/T71K2086.00MC11.23/LAPTOPS/MAG/ELI/ECCULD/PMC11166EK/DWC - MAG - CRUSHABLE167-07132252DWCLLW/T18K493.00MC2.27/CONSOLIDATION/ELI/MAG/HEA176-10685743DWCLLW/S6K96.60MC0.58T15/PRINTER176-10684144DWCLLW/T67K2337.00MC11.81/LAPTOPS/MAG/ELM/ELI/ECCULD/PMC30473EK/DWC - PIL-COL -CRUSHABLE176-74172851BOMLLW/S9K84.00MC1.06T44/DTPHBHIB VACCIN/COL/EMD/PIL176-02576350AMSLLW/S2K678.40MC3.46T5/72 BOXES - MEDI/COL/EAP/HEA/PIL/SPXCOR/T1176-02576346AMSLLW/T5K1736.00MC8.65/74 BOXES - MEDI/SPX/COL/PIL/EAPCOR/T1176-04249140HAMLLW/T1K3.00MC0.05/CONTROL MATERIA/COL/PIL/ECCCOR/X176-09688184IADLLW/T1K29.00MC0.04/DIPLOMATIC POUC/PIL/COL/DIP176-02576335AMSLLW/T1K296.00MC1.73/13 BOXES - MEDI/SPX/COL/PIL/EAP/HEACOR/T1176-07901165AMDLLW/T6K74.90MC0.44/PHARMACEUTICAL/PIL/COLCOR/T1ULD/PMC31696EK/DWC - ERT- PIL- CRUSHABLE167-05556213MANBLZ/S5K425.00MC6.30T8/REASERCH EQPMNT/HEA176-03222170AMSLLW/T1K17.60MC0.10/PHARMACEUTICALS/PIL/CRT/SPXCOR/X176-03498364AMSLLW/T1K187.40MC1.19/PHARMACEUTICALS/HEA/PIL/CRT/SPXCOR/XULD/PMC32590EK/DWC167-07132274DWCLLW/S23K448.96MC3.35T25/COMPUTER ACCS176-10678813DWCLLW/T1K29.00MC0.11/BOPP FILM176-10629673DWCLLW/T57K1554.00MC6.27/MOBILE PHONES/ELI/ECC"""

    # Generate XML
    xml_output = generate_xml_from_text(sample_input)

    # Print or save to file
    print(xml_output)

    # Optionally save to file
    with open("generated_manifest.xml", "w", encoding="utf-8") as f:
        f.write(xml_output)
