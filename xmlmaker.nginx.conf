server {
    listen 80;
    server_name xmlmaker.malawicargo.online;

    access_log /var/log/nginx/xmlmaker-access.log;
    error_log /var/log/nginx/xmlmaker-error.log;

    # Serve static files
    location / {
        root /var/www/cargo-handling-system/python/xmlmakerapp/frontend/static;
        index index.html;
        try_files $uri $uri/ /index.html;
    }

    # Proxy API requests to the backend
    location /api/ {
        proxy_pass http://localhost:5001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
