# IATA XML Generator

A web-based application for converting flight manifest data in condensed format to IATA XML format.

## Features

- Modern web interface built with React and Bootstrap
- Backend API powered by Flask
- Convert flight manifest data to IATA XML format
- Syntax highlighting for XML output
- Download generated XML files
- Sample data loading

## Project Structure

```
xmlmakerapp/
├── backend/               # Flask backend
│   ├── app.py             # API endpoints
│   ├── requirements.txt   # Python dependencies
│   └── venv/              # Python virtual environment
├── frontend/              # React frontend
│   ├── public/            # Static assets
│   ├── src/               # React components
│   ├── index.html         # HTML template
│   └── package.json       # Node.js dependencies
├── samples/               # Sample data files
│   ├── xffm/              # Sample XML files
│   └── xffm-sample.txt    # Sample input data
├── flight_manifest_generator.py  # XML generation logic
└── run.sh                 # Script to run both servers
```

## Prerequisites

- Python 3.6+
- Node.js 14+
- npm 6+

## Installation

1. Clone the repository:
   ```
   git clone <repository-url>
   cd xmlmakerapp
   ```

2. Set up the backend:
   ```
   cd backend
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   pip install -r requirements.txt
   ```

3. Set up the frontend:
   ```
   cd ../frontend
   npm install
   ```

## Running the Application

You can run both the backend and frontend servers with a single command:

```
./run.sh
```

Or run them separately:

1. Start the backend server:
   ```
   cd backend
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   python app.py  # Runs on port 5001
   ```

2. Start the frontend development server:
   ```
   cd frontend
   npm run dev
   ```

3. Open your browser and navigate to:
   ```
   http://localhost:5173/
   ```

## Usage

1. Enter flight manifest data in the input field or click "Load Sample" to use sample data.
2. Click "Generate XML" to convert the data to IATA XML format.
3. The generated XML will be displayed with syntax highlighting.
4. Click "Download XML" to save the generated XML to a file.

## API Endpoints

The backend API runs on port 5001:

- `GET http://localhost:5001/api/sample`: Returns sample flight manifest data
- `POST http://localhost:5001/api/generate-xml`: Converts input text to XML format
- `GET http://localhost:5001/api/health`: Health check endpoint
- `GET http://localhost:5001/api/logs`: Returns the last 100 lines of the log file
- `GET http://localhost:5001/api/debug/generator-info`: Returns debug information about the generator module

## License

This project is licensed under the MIT License - see the LICENSE file for details.
