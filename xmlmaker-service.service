[Unit]
Description=XML Maker Application Backend
After=network.target

[Service]
User=cargo
Group=www-data
WorkingDirectory=/var/www/cargo-handling-system/python/xmlmakerapp/backend
Environment="PATH=/var/www/cargo-handling-system/python/xmlmakerapp/backend/venv/bin"
Environment="PYTHONPATH=/var/www/cargo-handling-system/python/xmlmakerapp"
ExecStart=/usr/bin/python3 /var/www/cargo-handling-system/python/xmlmakerapp/backend/app.py
Restart=always

[Install]
WantedBy=multi-user.target
